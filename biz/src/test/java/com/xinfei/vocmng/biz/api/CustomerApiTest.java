package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.config.SmsTemplate;
import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryReq;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.remote.WorkOrderRemoteService;
import com.xinfei.vocmng.biz.rr.dto.CustomerDetailDto;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderDetailDto;
import com.xinfei.vocmng.biz.rr.request.GetCustomerRequest;
import com.xinfei.vocmng.biz.rr.request.PageQuery;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderMobileRequest;
import com.xinfei.vocmng.biz.service.CommunicateSummaryService;
import com.xinfei.vocmng.biz.service.CustomerService;
import com.xinfei.vocmng.itl.client.feign.impl.DataCenterClientService;
import com.xinfei.vocmng.itl.client.http.FundOrderFacade;
import com.xinfei.vocmng.itl.rr.ApplyLoanOrderReq;
import com.xinfei.vocmng.itl.rr.BaseUserCenterRequest;
import com.xinfei.vocmng.itl.rr.BaseUserCenterResponse;
import com.xinfei.vocmng.itl.rr.MobileGetData;
import com.xinfei.vocmng.itl.rr.OnlineProofResp;
import com.xinfei.vocmng.itl.rr.SmsTemplatedResponse;
import com.xinfei.vocmng.itl.util.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CustomerApiTest {

    @Resource
    private CustomerApi customerApi;

    @Resource
    private CustomerService customerService;

    @Resource
    private DataCenterClientService dataCenterClientService;

    @Resource
    private WorkOrderRemoteService workOrderRemoteService;

    @Resource
    private CommunicateSummaryService communicateSummaryService;

    @Test
    public void getMobile() {
        MobileGetData mobile = dataCenterClientService.mobileGet("me6dd0200ee24eba75e28da15b5a116b8");
        System.out.println(mobile);
    }

    @Test
    public void getUserNoList() {
    }

    @Test
    public void getCustomer() {
        GetCustomerRequest request = new GetCustomerRequest();
//        request.setMobile("18638860774");
//        request.setCustNo("CTL0abb63f0e71d70f7e6e366cff083cefdd");
//        request.setUserNo("1639203089097252643");
        request.setUserNo("1639203089095980520");

        ApiResponse<CustomerDetailDto> response = customerApi.getCustomer(request);
        System.out.println(response);
    }

    @Test
    public void sendMessage() {
    }

    @Test
    public void queryMessageTemplated() {
        PageQuery request = new PageQuery();
        request.setCurrentPage(1);
        request.setPageSize(10);

        ApiResponse<SmsTemplatedResponse> response = null;
        System.out.println(response);
    }

    @Test
    public void getMessageTemplates() {
        List<SmsTemplate> smsTemplateList = customerService.getSmsTemplates();
        System.out.println(smsTemplateList);
    }

    @Test
    public void queryWorkOrderList() {
        QueryWorkOrderMobileRequest request = new QueryWorkOrderMobileRequest();
        request.setMobiles(Collections.singletonList("13111111111"));
        PageResultResponse<WorkOrderDetailDto> resultResponse =workOrderRemoteService.queryWorkMobileDetail(request);
        System.out.println(resultResponse);
    }

    @Test
    public void queryCommunicateSummaryList() {
        CommunicateSummaryReq communicateSummaryReq = new CommunicateSummaryReq();
        communicateSummaryReq.setUserNos(Collections.singletonList("1939303089100716237"));
        communicateSummaryReq.setMobiles(Collections.singletonList("13267036676"));
        PageResultResponse<CommunicateSummaryResp> resultResponse = communicateSummaryService.list(communicateSummaryReq);
        System.out.println(resultResponse);
    }
}