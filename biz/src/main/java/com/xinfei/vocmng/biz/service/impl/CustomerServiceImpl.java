/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.Gson;
import apollo.com.google.gson.JsonObject;
import apollo.com.google.gson.reflect.TypeToken;
import com.alibaba.fastjson2.util.DateUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoResponse;
import com.xinfei.cisaggs.facade.rr.StandardCustomerInfoResponse;
import com.xinfei.cisaggs.facade.rr.dto.ContactDTO;
import com.xinfei.cisaggs.facade.rr.dto.CustBasicInfoDTO;
import com.xinfei.cisaggs.facade.rr.dto.FamilyEduDTO;
import com.xinfei.cisaggs.facade.rr.dto.JobDTO;
import com.xinfei.cisaggs.facade.rr.dto.StandardUserDTO;
import com.xinfei.lendtrade.facade.rr.ManageUserLatestOrderRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageUserLatestOrderDTO;
import com.xinfei.repaytrade.facade.rr.request.list.ListSaveRequest;
import com.xinfei.repaytrade.facade.rr.request.list.ListUpdateRequest;
import com.xinfei.repaytrade.facade.rr.response.list.ListInfoResponse;
import com.xinfei.supervip.interfaces.model.admin.dto.VipUserStatusAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.UserVipStatusDto;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.config.SmsTemplate;
import com.xinfei.vocmng.biz.config.SmsTemplateConfig;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.mapstruct.CustomerConverter;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.remote.NPayService;
import com.xinfei.vocmng.biz.rr.dto.BankDto;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.AccountService;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.service.CustomerService;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.biz.util.IDCardUtil;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.feign.*;
import com.xinfei.vocmng.itl.client.feign.impl.*;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.*;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.cis.query.facade.dto.standard.response.CustNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.ext.info.model.Challenge;
import com.xyf.ext.info.model.Contact;
import com.xyf.user.auth.dto.response.OcrFaceImagesResponse;
import com.xyf.user.facade.common.model.PageResult;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CustomerServiceImpl, v 0.1 2023-12-20 19:34 junjie.yan Exp $
 */

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private LcsFeignService lcsFeignService;

    @Resource
    private BankFeignService bankFeignService;

    @Resource
    private CreditFeignService creditFeignService;

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private CisExtFacadeClient cisExtFacadeClient;

    @Resource
    private CisAuthFacadeClient cisAuthFacadeClient;

    @Resource
    private UserLabelMappingMapper userLabelMappingMapper;

    @Resource
    private LabelSolutionsService labelSolutionsService;

    @Resource
    private SmsFeignService smsFeignService;

    @Resource
    private SmsTemplateConfig smsTemplateConfig;

    @Resource
    private AccountService accountService;

    @Resource
    private UdeskClientService udeskClientService;

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClientImpl;

    @Resource
    private VipFacadeClientImpl vipFacadeClient;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private CommonService commonService;

    @Resource
    private CommunicateSummaryMapper communicateSummaryMapper;

    @Resource
    private BankCardFacadeService bankCardFacadeService;

    @Resource
    private VocConfig vocConfig;

    @Resource
    private LogOffService logOffService;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private RepayListClient repayListClient;


    @Resource
    private LoginUserConfig loginUserConfig;

    @Resource
    private NPayService nPayService;

    @Resource
    private FeatureQueryClientImpl featureQueryClientImpl;

    @Resource
    private FeaturePlatformClientImpl featurePlatformClient;

    @Resource
    private UserLabelService userLabelService;


    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final com.google.gson.Gson gson = new com.google.gson.Gson();
    private final Type typeListString = new TypeToken<List<String>>() {
    }.getType();

    public Paging<CustomerDto> getCustomerList(GetCustomerListRequest request) {
        String custNo = null;
        Paging<CustomerDto> listPaging = new Paging<>();
        if (request.getIdCard() != null) {
            CustNoDTO custNoDTO = cisFacadeClient.queryCustNoByIdNo(request.getIdCard());
            if (custNoDTO != null) {
                custNo = custNoDTO.getCustNo();
            }
        }

        List<UserSearchDTO> needUserSearch = new ArrayList<>();
        PageResult<UserSearchDTO> pageResult;
        int n = 1;
        do {
            pageResult = cisFacadeClient.queryUserList(request.getMobile(), custNo, request.getUserNo(), n, request.getPageSize());
            if (CollectionUtils.isEmpty(pageResult.getList())) {
                break;
            }
            needUserSearch.addAll(pageResult.getList());
            n++;
        } while (true);

        if (StringUtils.isNotEmpty(request.getMobile())) {
            List<UserSearchDTO> custNoUsers = commonService.getUserByMobileAndCust(request.getMobile());
            if (CollectionUtils.isNotEmpty(custNoUsers)) {
                needUserSearch.addAll(custNoUsers);
                needUserSearch = needUserSearch.stream()
                        .collect(Collectors.toMap(
                                UserSearchDTO::getUserNo,
                                user -> user,
                                (existing, replacement) -> existing
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(needUserSearch)) {
            return listPaging;
        }

        if (StringUtils.isNotBlank(request.getUserNo())) {
            needUserSearch = needUserSearch.stream().filter(r -> Long.parseLong(request.getUserNo()) == r.getUserNo()).collect(Collectors.toList());
        }

        if (request.getStatus() != null && request.getStatus() != 2) {
            needUserSearch = needUserSearch.stream().filter(r -> request.getStatus().equals(r.getStatus())).collect(Collectors.toList());
        }
        //状态为：注销中
        if (request.getStatus() != null && request.getStatus() == 2) {
            needUserSearch = needUserSearch.stream().filter(r -> request.getStatus().equals(r.getOperateStatus())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(request.getRegisterApp())) {
            needUserSearch = needUserSearch.stream().filter(r -> request.getRegisterApp().equals(r.getApp())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(request.getSourceChannel())) {
            needUserSearch = needUserSearch.stream().filter(r -> request.getSourceChannel().equals(r.getSourceChannel())).collect(Collectors.toList());
        }

        listPaging.setTotal(needUserSearch.size());

        needUserSearch = needUserSearch.stream()
                .skip((long) (request.getCurrentPage() - 1) * request.getPageSize())
                .limit(request.getPageSize()).
                collect(Collectors.toList());

        listPaging.setCurrentPage(request.getCurrentPage());
        listPaging.setPageSize(request.getPageSize());

        listPaging.setList(getCustomers(needUserSearch));

        return listPaging;
    }

    /**
     * 页面：
     * ① 填手机号+app+身份证，传给后端 app+身份证
     * ② 填手机号+身份证，传给后端 身份证
     * ③ 填app+身份证，传给后端 app+身份证
     * ④ 填手机号+app，传给后端 手机号+app
     * ⑤ 填手机号，传给后端 手机号
     * ⑥ 填身份证，传给后端 身份证
     * ⑦ 不能只填app
     * <p>
     * 服务端：
     * 1. ④根据手机号+注册APP，确定唯一UserNo
     * 2. ⑤只传手机号，根据手机号查询cis返回UserNo列表，第一条为订单中创建时间最晚的UserNo
     * 3. ①②③⑥只要包含身份证号，根据身份证号查询cis返回UserNo列表，第一条为订单中创建时间最晚的UserNo
     * 4. ⑦不能只传注册APP
     *
     * @param request
     * @return
     */
    @Override
    public List<UserNoAppDto> getUserNoList(GetUserNoListRequest request) {
        List<UserNoAppDto> userNoAppDtos = new ArrayList<>();
        //手机号+注册APP，确定唯一UserNo
        if (!StringUtils.isEmpty(request.getMobile()) && !StringUtils.isEmpty(request.getRegisterApp())) {
            UserNoDTO userNoDTO = cisFacadeClient.getUserNoByMobileAndApp(request.getMobile(), request.getRegisterApp());
            if (userNoDTO == null) {
                return userNoAppDtos;
            }
            UserNoAppDto userNoAppDto = new UserNoAppDto();
            userNoAppDto.setApp(userNoDTO.getApp());
            userNoAppDto.setUserNo(userNoDTO.getUserNo() == null ? null : userNoDTO.getUserNo().toString());
            userNoAppDtos.add(userNoAppDto);
            return userNoAppDtos;
        }

        //手机号/身份证存在，不存在手机号+身份证都传的情况
        if (!StringUtils.isEmpty(request.getMobile()) || !StringUtils.isEmpty(request.getIdCard())) {
            userNoAppDtos.addAll(getUserNoByMobileId(request.getMobile(), request.getIdCard(), request.getUserNo()));
            return userNoAppDtos;
        }
        return userNoAppDtos;
    }

    private List<CustomerDto> getCustomers(List<UserSearchDTO> userSearchDTOS) {
        List<CustomerDto> customers = new ArrayList<>();
        for (UserSearchDTO userSearchDTO : userSearchDTOS) {
            CustomerDto customerDto = CustomerConverter.INSTANCE.userToCustomer(userSearchDTO);
            customerDto.setTel(userSearchDTO.getMobile());
            customerDto.setIdNoMd(userSearchDTO.getIdNo());
            if (!StringUtils.isEmpty(userSearchDTO.getCustNo())) {
                try {
                    //获取用户借款成功订单数
                    customerDto.setSucLoanOrdersNum(getSucLoanOrdersNum(userSearchDTO.getCustNo(), userSearchDTO.getUserNo().toString()));
                } catch (ClientException e) {
                    log.warn("setSucLoanOrdersNum warn,msg:{},cusrNo:{}", e.getMessage(), userSearchDTO.getCustNo());
                }
            }
            customers.add(customerDto);
        }
        return customers;
    }

    private Integer getSucLoanOrdersNum(String custNo, String userNo) {
        LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
        loanPlanRequest.setCustNos(Collections.singletonList(custNo));
        List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
        loanPlanResponses = loanPlanResponses.stream().filter(r -> r.getIsProfitLoan() == 1).collect(Collectors.toList());
        if (!StringUtils.isEmpty(userNo)) {
            loanPlanResponses = loanPlanResponses.stream().filter(r -> userNo.equals(r.getUserNo())).collect(Collectors.toList());
            return loanPlanResponses.size();
        }

        return loanPlanResponses.size();
    }

    public CustomerDetailDto getCustomerDetail(GetCustomerRequest request) {
        PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, null, request.getUserNo(), 1, 10);
        if (CollectionUtils.isEmpty(pageResult.getList())) {
            return null;
        }

        UserSearchDTO userSearch = pageResult.getList().get(0);
        String custNo = userSearch.getCustNo();
        Long userNo = userSearch.getUserNo();
        String app = userSearch.getApp();
        String mobile = userSearch.getMobile();

        //获取用户基本信息+用户注册时间
        CustomerDetailDto customerDetail = CustomerConverter.INSTANCE.userToCustomerDetail(userSearch);
        customerDetail.setIdNoTrans(customerDetail.getIdNo());
        customerDetail.setCustNo(custNo);
        customerDetail.setTel(userSearch.getMobile());
        customerDetail.setEnCodeTel(cisFacadeClientService.getEncodeMobileLocal(userSearch.getMobile()));

        /*
         * 根据 userNo 查询信息
         */
        getCustomerDetailByUserNo(customerDetail, userNo);

        /*
         * custNo 不为空时可查的信息
         */
        if (!StringUtils.isEmpty(custNo)) {
            getCustomerDetailByCustNo(customerDetail, userNo, custNo, app);
        }

        //获取用户客服系统信息
        if (!StringUtils.isEmpty(mobile)) {
 //           customerDetail.setLabels(getBlackProductSolution(mobile));
//            getCallAndImLogs(customerDetail, mobile);
            getSummaryLogs(customerDetail, userNo);
            logOffQuery(customerDetail, mobile, userSearch.getApp());
        }

        if (StringUtils.isNotEmpty(userSearch.getIdNo())) {
            customerDetail.setAge(IDCardUtil.getAge(userSearch.getIdNo()));
            customerDetail.setBirthday(IDCardUtil.getBirthday(userSearch.getIdNo()));
        }

        //获取用户实时标签
        if (customerDetail.getLabels() == null) {
            customerDetail.setLabels(new ArrayList<>());
        }

        customerDetail.setLabels(userLabelService.getUserLabel(customerDetail.getUserNo()));

        return customerDetail;
    }


    @Override
    public List<BankDto> getAllBankCardList(String userNo) {
        List<BankCardResponse> response = bankCardFacadeService.queryBankCardListHistoryByUserNo(userNo);
        List<BankDto> bankDtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(response)) {
            return bankDtos;
        }

        for (BankCardResponse bankCardResponse : response) {
            BankDto bankDto = CustomerConverter.INSTANCE.bankCardResponseToBankDto(bankCardResponse);
            bankDtos.add(bankDto);
        }
        bankDtos = bankDtos.stream().sorted(Comparator.comparing(BankDto::getBindCardTime).reversed()).collect(Collectors.toList());

        return bankDtos;
    }

    @Override
    public ImagesDto getImagesDetail(String userNo, String custNo) {
        OcrFaceImagesResponse response = cisAuthFacadeClient.queryOcrFaceImages(Long.parseLong(userNo), custNo, null);
        return CustomerConverter.INSTANCE.imagesResponseToImagesDto(response);
    }

    private void getCallAndImLogs(CustomerDetailDto customerDetail, String mobile) {
        try {
            customerDetail.setCallLogs(udeskClientService.customerCallLogs(mobile));
        } catch (Exception e) {
            log.warn("getCallLogs warn,mobile:{}", mobile);
        }

        try {
            customerDetail.setImLogs(udeskClientService.customerImLogs(mobile));
        } catch (Exception e) {
            log.warn("getImLogs warn,mobile:{}", mobile);
        }
    }

    private void getSummaryLogs(CustomerDetailDto customerDetail, Long userNo) {
        try {
            LocalDate endTime = LocalDate.now();
            LocalDate startTime = endTime.plusDays(-7);
            customerDetail.setSummaryLogs(communicateSummaryMapper.getSummariesSevenDay(userNo, startTime));
        } catch (Exception e) {
            log.warn("getSummaryLogs warn,userNo:{}", userNo);
        }
    }

    @Override
    public Boolean sendMessage(SendMessageRequest request) {
        Map<String, Object> data = null;

        List<String> specialPublicAccountSMS = gson.fromJson(vocConfig.getSpecialPublicAccountSMS(), typeListString);
        if (specialPublicAccountSMS.contains(request.getTemplateId())) {
            //如发送短信的模板对公模板，则获取对公账户信息
            PublicAccountRequest publicAccountRequest = new PublicAccountRequest();
            publicAccountRequest.setUserNo(request.getUserNo());
            PublicAccountInfo publicAccountInfo = nPayService.queryPublicAccountInfo(publicAccountRequest);
            if (publicAccountInfo != null) {
                data = new HashMap<>();
                data.put("#kf_bankAccountName", publicAccountInfo.getBankAccountName());
                data.put("#kf_bankCardNo", publicAccountInfo.getBankCardNo());
                data.put("#kf_bankName", publicAccountInfo.getBankName());
                data.put("#kf_openBankBranch", publicAccountInfo.getOpenBankBranch());
                data.put("#kf_openCityName", publicAccountInfo.getOpenCityName());
                data.put("#kf_openBankNo", publicAccountInfo.getOpenBankNo());
            }
        }
        String mobile = request.getMobile();
        if (StringUtils.isBlank(mobile)) {
            mobile = cisFacadeClient.queryMobileByUserNo(Long.parseLong(request.getUserNo())).getMobile();
        }
        return smsFeignService.smsSend(mobile, request.getTemplateId(), request.getApp(), data);
    }

    public List<SmsTemplate> getSmsTemplates() {
        List<JsonObject> smsTemplates = smsTemplateConfig.getSmsTemplates();
        List<SmsTemplate> smsTemplateList = new ArrayList<>();
        List<String> specialPublicAccountSMS = gson.fromJson(vocConfig.getSpecialPublicAccountSMS(), typeListString);
        for (JsonObject jsonObject : smsTemplates) {
            SmsTemplate user = new Gson().fromJson(jsonObject, SmsTemplate.class);
            if (specialPublicAccountSMS.contains(user.getTemplateId())) {
                PublicAccountRequest publicAccountRequest = new PublicAccountRequest();
                PublicAccountInfo publicAccountInfo = nPayService.queryPublicAccountInfo(publicAccountRequest);
                if (publicAccountInfo != null) {
                    String content = user.getContent();
                    //替换规则
                    Map<String, String> replacements = new HashMap<>();
                    replacements.put("#kf_bankAccountName", publicAccountInfo.getBankAccountName());
                    replacements.put("#kf_bankCardNo", publicAccountInfo.getBankCardNo());
                    replacements.put("#kf_bankName", publicAccountInfo.getBankName());
                    replacements.put("#kf_openBankBranch", publicAccountInfo.getOpenBankBranch());
                    replacements.put("#kf_openCityName", publicAccountInfo.getOpenCityName());
                    replacements.put("#kf_openBankNo", publicAccountInfo.getOpenBankNo());
                    // 遍历替换规则，进行替换
                    for (Map.Entry<String, String> entry : replacements.entrySet()) {
                        content = content.replace(entry.getKey(), entry.getValue());
                    }
                    user.setContent(content);
                }
            }
            smsTemplateList.add(user);
        }
        return smsTemplateList;
    }

    private void getCustomerDetailByUserNo(CustomerDetailDto customerDetail, Long userNo) {
        //获取用户最后一次登录时间
        try {
            Date lastLoginTime = cisFacadeClient.queryLastLoginByUserNo(userNo).getLastLoginTime();
            if (lastLoginTime != null) {
                customerDetail.setLastLoginTime(LocalDateTime.ofInstant(lastLoginTime.toInstant(), ZoneId.of("Asia/Shanghai")));
            }
        } catch (Exception e) {
            log.warn("queryLastLoginByUserNo warn,userNo:{}", userNo);
        }


        try {
            customerDetail.setTaskQuantity(workOrderFeignClientImpl.getTaskByUserNo(userNo).getTotal());
        } catch (Exception e) {
            log.warn("getTaskByUserNo warn,userNo:{}", userNo);
        }

        try {
            UserVipStatusDto userVipStatusDto = vipFacadeClient.userVipStatus(userNo);
            if (Objects.nonNull(userVipStatusDto)) {
                customerDetail.setIsVip(userVipStatusDto.getIsVip());
                customerDetail.setRenewStatus(userVipStatusDto.getRenewStatus());
            }
            if (vocConfig.getIsSuperVipCard()) {
                VipUserStatusAdminDTO vipUserStatusDTO = vipFacadeClient.userSuperVipStatus(userNo);
                if (Objects.nonNull(vipUserStatusDTO)) {
                    customerDetail.setIsSuperVip(vipUserStatusDTO.getWhetherVip());
                    customerDetail.setSuperVipRenewStatus(vipUserStatusDTO.getWhetherRenew());
                }
            }
        } catch (Exception e) {
            log.warn("userVipStatus warn,userNo:{}", userNo);
        }
    }

    public void getCustomerDetailByCustNo(CustomerDetailDto customerDetail, Long userNo, String custNo, String app) {
        try {
            //获取用户身份证地址
            customerDetail.setIdAddress(cisFacadeClient.queryIdNoByCustNo(custNo).getIdAddress());
        } catch (ClientException e) {
            log.warn("setIdAddress warn,msg:{},custNo:{}", e.getMessage(), custNo);
        }

        try {
            //获取用户银行卡信息
            customerDetail.setBanks(getBankInfo(custNo, app));
        } catch (ClientException e) {
            log.warn("setBanks warn,msg:{},custNo:{}, app:{}", e.getMessage(), custNo, app);
        }

        try {
            //获取用户借款成功订单数
            customerDetail.setSucLoanOrdersNum(getSucLoanOrdersNum(custNo, userNo.toString()));
        } catch (ClientException e) {
            log.warn("setSucLoanOrdersNum warn,msg:{},custNo:{}", e.getMessage(), custNo);
        }

        try {
            //获取用户现金、消费额度
            getAmt(customerDetail, custNo, app);
        } catch (ClientException e) {
            log.warn("getAmt warn,msg:{},custNo:{}, app:{}", e.getMessage(), custNo, app);
        }

        try {
            //获取用户附属信息
            getCisExtInfo(customerDetail, custNo, app);
        } catch (ClientException e) {
            log.warn("getCisExtInfo warn, msg:{},custNo:{}, app:{}", e.getMessage(), custNo, app);
        }
    }

    private void getCisExtInfo(CustomerDetailDto customerDetail, String custNo, String app) {
        if (!vocConfig.getIsStandardBatch()) {
            Challenge challenge = cisExtFacadeClient.queryLatestUserExtInfoData(custNo, app);
            if (challenge == null) {
                return;
            }

            if (challenge.getFamilyEdu() != null && challenge.getFamilyEdu().getIsDeleted() == 0) {
                customerDetail.setFamilyEdu(CustomerConverter.INSTANCE.familyEduToFamilyEduDto(challenge.getFamilyEdu()));
            }

            if (challenge.getJob() != null && challenge.getJob().getIsDeleted() == 0) {
                customerDetail.setJob(CustomerConverter.INSTANCE.jobToJobDto(challenge.getJob()));
            }

            if (!CollectionUtils.isEmpty(challenge.getContacts())) {
                List<ContactDto> contactDtos = new ArrayList<>();
                for (Contact contact : challenge.getContacts()) {
                    if (contact != null && contact.getIsDeleted() == 0) {
                        ContactDto contactDto = CustomerConverter.INSTANCE.contactToContactDto(contact);
                        contactDtos.add(contactDto);
                    }
                }
                customerDetail.setContacts(contactDtos);
            }
        } else {
            StandardBatchCustomerInfoResponse standardBatchCustomerInfoResponse = cisExtFacadeClient.queryStandardBatchCustomerInfo(custNo, app ,null);
            if (standardBatchCustomerInfoResponse == null || CollectionUtils.isEmpty(standardBatchCustomerInfoResponse.getResponseList())) {
                return;
            }
            StandardCustomerInfoResponse standardCustomerInfoResponse = standardBatchCustomerInfoResponse
                    .getResponseList()
                    .stream()
                    .filter(r -> r.getApp().equals(app))
                    .findFirst()
                    .orElse(null);
            if (standardCustomerInfoResponse == null) {
                return;
            }

            CustBasicInfoDTO custBasicInfoDTO = standardCustomerInfoResponse.getCustBaseInfo();
            if (custBasicInfoDTO != null) {
                FamilyEduDTO familyEduDTO = custBasicInfoDTO.getFamilyEdu();
                customerDetail.setFamilyEdu(CustomerConverter.INSTANCE.newFamilyEduToFamilyEduDto(familyEduDTO));

                JobDTO jobDTO = custBasicInfoDTO.getJob();
                customerDetail.setJob(CustomerConverter.INSTANCE.newJobToJobDto(jobDTO));
            }

            if (CollectionUtils.isNotEmpty(standardCustomerInfoResponse.getContactList())) {
                List<ContactDto> contactDtos = new ArrayList<>();
                for (ContactDTO contact : standardCustomerInfoResponse.getContactList()) {
                    if (contact != null) {
                        ContactDto contactDto = CustomerConverter.INSTANCE.newContactToContactDto(contact);
                        if (contact.getMobileDTO() != null) {
                            contactDto.setContactMobileNo(contact.getMobileDTO().getMobile());
                        }
                        contactDtos.add(contactDto);
                    }
                }
                customerDetail.setContacts(contactDtos);
            }
        }
    }

    public void getAmt(CustomerDetailDto customerDetail, String custNo, String app) {
        AmsAccountInfo consumerAccountInfo = creditFeignService.amountInfo(custNo, "mvp");
        customerDetail.setConsumerCreditAmt(consumerAccountInfo.getCreditAmt() == null ? null : consumerAccountInfo.getCreditAmt().multiply(new BigDecimal("0.01")));
        customerDetail.setConsumerDebitAmt(consumerAccountInfo.getDebitAmt() == null ? null : consumerAccountInfo.getDebitAmt().multiply(new BigDecimal("0.01")));

        AmsAccountInfo cashAccountInfo = creditFeignService.amountInfo(custNo, app);
        customerDetail.setCashCreditAmt(cashAccountInfo.getCreditAmt() == null ? null : cashAccountInfo.getCreditAmt().multiply(new BigDecimal("0.01")));
        customerDetail.setCashDebitAmt(cashAccountInfo.getDebitAmt() == null ? null : cashAccountInfo.getDebitAmt().multiply(new BigDecimal("0.01")));
    }


    public List<BankDto> getBankInfo(String custNo, String app) {
        List<BankCardDto> bankCardDtos = bankFeignService.queryLoanQuery(custNo, app);
        if (CollectionUtils.isEmpty(bankCardDtos)) {
            return null;
        }
        List<BankDto> banks = new ArrayList<>();
        for (BankCardDto bankCardDto : bankCardDtos) {
            BankDto bankDto = CustomerConverter.INSTANCE.bankCardDtoToBankDto(bankCardDto);
            banks.add(bankDto);
        }
        banks = banks.stream().sorted(Comparator.comparing(BankDto::getStatus)).collect(Collectors.toList());
        return banks;
    }

    /**
     * 1. 只传手机号，返回UserNo列表，第一条为订单中创建时间最晚的UserNo
     * 2. 只传身份证号，返回UserNo列表，第一条为订单中创建时间最晚的UserNo
     *
     * @param
     * @return
     */
    public List<UserNoAppDto> getUserNoByMobileId(String mobile, String idCard, String userNo) {
        String custNo = null;
        if (!StringUtils.isEmpty(idCard)) {
            CustNoDTO custNoDTO = cisFacadeClient.queryCustNoByIdNo(idCard);
            if (custNoDTO != null) {
                custNo = custNoDTO.getCustNo();
            }
        }
        PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(mobile, custNo, null, 1, 30);
        List<UserNoAppDto> userNoAppDtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(pageResult.getList())) {
            return userNoAppDtos;
        }

        //只传身份证号，返回UserNo列表，第一条为订单相关的UserNo
        ManageUserLatestOrderRequest request = new ManageUserLatestOrderRequest();
        if (!StringUtils.isEmpty(idCard) && !StringUtils.isEmpty(custNo)) {
            request.setCustNo(custNo);
        } else if (!StringUtils.isEmpty(mobile)) {
            //只传手机号，返回UserNo列表，第一条UserNo取 所有userNo对应的订单中 最晚的一条订单的userNo
            List<Long> userNos = pageResult.getList().stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList());
            //取每个userNo对应的最新一条订单
            if (CollectionUtils.isEmpty(userNos)) {
                return userNoAppDtos;
            }
            request.setUserNos(userNos);
        } else {
            return userNoAppDtos;
        }

        if (StringUtils.isEmpty(userNo)) {
            //取第一条为订单中创建时间最晚的UserNo
            ManageUserLatestOrderDTO manageUserLatestOrderDTO = lendQueryFacadeClient.userLatestOrder(request);
            if (Objects.nonNull(manageUserLatestOrderDTO) && Objects.nonNull(manageUserLatestOrderDTO.getUserNo())) {
                List<UserSearchDTO> firstUser = pageResult.getList().stream().filter(r -> r.getUserNo().equals(manageUserLatestOrderDTO.getUserNo())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(firstUser)) {
                    UserNoAppDto userNoAppDto = new UserNoAppDto();
                    userNoAppDto.setUserNo(manageUserLatestOrderDTO.getUserNo().toString());
                    userNoAppDto.setApp(manageUserLatestOrderDTO.getApp());
                    userNoAppDto.setMobile(firstUser.get(0).getMobile());
                    userNoAppDtos.add(userNoAppDto);
                    pageResult.setList(pageResult.getList().stream().filter(r -> !r.getUserNo().equals(manageUserLatestOrderDTO.getUserNo())).collect(Collectors.toList()));
                }
            }
        } else {
            //取第一条为用户列表页跳转过来的userNo
            List<UserSearchDTO> firstUser = pageResult.getList().stream().filter(r -> r.getUserNo().toString().equals(userNo)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(firstUser)) {
                UserNoAppDto userNoAppDto = new UserNoAppDto();
                userNoAppDto.setUserNo(firstUser.get(0).getUserNo().toString());
                userNoAppDto.setApp(firstUser.get(0).getApp());
                userNoAppDto.setMobile(firstUser.get(0).getMobile());
                userNoAppDtos.add(userNoAppDto);
                pageResult.setList(pageResult.getList().stream().filter(r -> !r.getUserNo().toString().equals(userNo)).collect(Collectors.toList()));
            }
        }

        //将剩余的userNo添加
        pageResult.getList().forEach(r -> {
            UserNoAppDto userNoAppDto = new UserNoAppDto();
            userNoAppDto.setUserNo(r.getUserNo().toString());
            userNoAppDto.setApp(r.getApp());
            userNoAppDto.setMobile(r.getMobile());
            userNoAppDtos.add(userNoAppDto);
        });
        return userNoAppDtos;
    }

    /**
     * 客户注销
     */
    @Override
    public Boolean userLogOff(LogOffReq logOffReq) {

        if (!logOffReq.getImmediate()) {
            //客户注销状态检查
            LogOffCheckRequest request = new LogOffCheckRequest();
            request.setUserNo(Long.parseLong(logOffReq.getUserNo()));
            LogOffDto logOffDto = logOffService.logOffCheck(request);
            if (Objects.isNull(logOffDto) || !logOffDto.getEligible()) {
                throw new IgnoreException(TechplayErrDtlEnum.USER_LOG_OFF_CHECK_ERROR, logOffDto.getReason());
            }
        }

        LogOffRequest logOffRequest = new LogOffRequest();
        logOffRequest.setApp(logOffReq.getApp());
        logOffRequest.setMobile(logOffReq.getMobile());
        logOffRequest.setRemark(logOffReq.getRemark());
        logOffRequest.setOperatorName(logOffReq.getOperatorName());
        //false-延迟注销 true-立即注销
        logOffRequest.setImmediate(logOffReq.getImmediate());

        //客户注销
        return logOffService.logOff(logOffRequest);
    }

    /**
     * 客户撤消注销
     */
    @Override
    public Boolean userLogOffCancel(LogOffReq logOffReq) {
        LogOffCancelRequest logOffRequest = new LogOffCancelRequest();
        logOffRequest.setApp(logOffReq.getApp());
        logOffRequest.setMobile(logOffReq.getMobile());
        logOffRequest.setOperatorName(logOffReq.getOperatorName());
        logOffRequest.setIsUnblock(true);
        return logOffService.logOffCancel(logOffRequest);
    }

    @Override
    public Boolean repayListQuery(String custNo) {
        List<ListInfoResponse> list = repayListClient.repayListQuery(custNo);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        } else if ("ENABLE".equals(list.get(0).getStatus())) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean repayListUpdate(RepayListCreateReq req) {
        List<ListInfoResponse> list = repayListClient.repayListQuery(req.getCustNo());
        if (CollectionUtils.isNotEmpty(list)) {
            ListInfoResponse listInfoResponse = list.get(0);
            if (req.getStatus().equals(listInfoResponse.getStatus())) {
                throw new IgnoreException(TechplayErrDtlEnum.UPDATE_ERROR, "名单状态已发生变更，请刷新页面！");
            }
            ListUpdateRequest updateRequest = new ListUpdateRequest();
            updateRequest.setListId(listInfoResponse.getListId());
            updateRequest.setListValue(req.getCustNo());
            updateRequest.setListValueType("CUST_NO");
            updateRequest.setBizScene("MULTI_CARD_REPAYMENT");
            updateRequest.setOperatedBy(req.getOperatedBy());
            updateRequest.setBizChannel("VOCMNG");
            if ("DISABLE".equals(req.getStatus())) {
                updateRequest.setEffectiveStart(listInfoResponse.getEffectiveStart());
                updateRequest.setEffectiveEnd(listInfoResponse.getEffectiveEnd());
            } else {
                updateRequest.setEffectiveStart(new Date());
                if (req.getIsForeverClose()) {
                    updateRequest.setEffectiveEnd(DateUtils.parseDate("2099-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss"));
                } else {
                    updateRequest.setEffectiveEnd(LocalDateTimeUtils.parseDateByLocalDateTime(LocalDate.now().plusDays(req.getCloseDay() + 1).atStartOfDay()));
                }
            }
            updateRequest.setStatus(req.getStatus());
            return repayListClient.repayListUpdate(updateRequest);
        } else {
            ListSaveRequest listSaveRequest = new ListSaveRequest();
            listSaveRequest.setListValue(req.getCustNo());
            listSaveRequest.setListValueType("CUST_NO");
            listSaveRequest.setBizScene("MULTI_CARD_REPAYMENT");
            listSaveRequest.setOperatedBy(req.getOperatedBy());
            listSaveRequest.setEffectiveStart(new Date());
            if (req.getIsForeverClose()) {
                listSaveRequest.setEffectiveEnd(DateUtils.parseDate("2099-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss"));
            } else {
                listSaveRequest.setEffectiveEnd(LocalDateTimeUtils.parseDateByLocalDateTime(LocalDate.now().plusDays(req.getCloseDay() + 1).atStartOfDay()));
            }
            listSaveRequest.setBizChannel("VOCMNG");
            listSaveRequest.setStatus(req.getStatus());
            return repayListClient.repayListCreate(listSaveRequest);
        }
    }

    public List<UserRelateNumber> queryUserRelateNumber(QueryUserRelateNumberRequest queryUserRelateNumberRequest) {
        List<String> userNumberList = new ArrayList<>();
        List<String> appList = new ArrayList<>();
        appList.add("xyf01");
        appList.add("fxk");
        appList.add("fxk01");
        appList.add("cxh");
        appList.add("lqqx");
        StandardBatchCustomerInfoResponse standardBatchCustomerInfoResponse = cisExtFacadeClient.queryStandardBatchCustomerInfo(queryUserRelateNumberRequest.getCustNo(), null ,appList);
        if (standardBatchCustomerInfoResponse == null || CollectionUtils.isEmpty(standardBatchCustomerInfoResponse.getResponseList())) {
            return null;
        }
        for (StandardCustomerInfoResponse standardCustomerInfoResponse : standardBatchCustomerInfoResponse.getResponseList()) {
            if(CollectionUtils.isNotEmpty(standardCustomerInfoResponse.getUserList())){
                for (StandardUserDTO standardUserDTO : standardCustomerInfoResponse.getUserList()){
                    userNumberList.add(standardUserDTO.getMobileNo());
                }
            }
        }
        List<String> uniqueList = userNumberList.stream()
                .distinct()  // 去重
                .collect(Collectors.toList());

        List<UserRelateNumber> numberList = uniqueList.stream()
                .map(mobile -> {
                    UserRelateNumber userRelateNumber = new UserRelateNumber();
                    userRelateNumber.setMobile(mobile);
                    userRelateNumber.setMobileEncrypted(mobile);
                    return userRelateNumber;
                })
                .collect(Collectors.toList());
        return numberList;
    }
    //获取用户注销信息
    public void logOffQuery(CustomerDetailDto customerDetailDto, String mobile, String app) {
        List<LogOffQueryDto> list = logOffService.logOffQuery(app, mobile);
        if (CollectionUtils.isNotEmpty(list)) {
            LogOffQueryDto logOffQueryDto = list.get(0);
            customerDetailDto.setLogOffRemark(logOffQueryDto.getRemark());
            customerDetailDto.setLogOffTime(logOffQueryDto.getUpdatedTime());
            customerDetailDto.setLogOffStatus(logOffQueryDto.getStatus());
            customerDetailDto.setLogOffOperatorName(logOffQueryDto.getOperatorName());
        }
    }



    private static void appSort(GetUserNoListRequest request, List<UserNoAppDto> userNoAppDtos) {
        String targetApp = request.getRegisterApp();
        String targetMobile = request.getMobile();
        userNoAppDtos.sort(Comparator
                .comparing((UserNoAppDto dto) -> dto.getApp().equals(targetApp) ? 0 : 1)  // 首先按 app 排序：匹配的 app 排在前面
                .thenComparing((UserNoAppDto dto) -> {  // 在 app 相同的情况下，根据 mobile 排序
                    if (dto.getApp().equals(targetApp) && StringUtils.isNotBlank(targetMobile)) {
                        return dto.getMobile().equals(targetMobile) ? 0 : 1;
                    }
                    return 1;
                }));
    }
}