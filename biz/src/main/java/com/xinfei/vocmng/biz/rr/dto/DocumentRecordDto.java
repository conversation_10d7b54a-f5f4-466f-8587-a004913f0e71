/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/8/13 14:10
 * 用戶优惠券 UserCouponDetailDto
 */
@Data
public class DocumentRecordDto {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("userNo")
    private String userNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("扣款单号")
    private String deductionOrderNo;

    @ApiModelProperty("文件类型，1:信飞结清证明，2:资方结清证明，3:借款凭证 ，4:明珠结清证明，5:居间协议，6:还款凭证，7:退款凭证")
    private Integer type;

    @ApiModelProperty("状态，1:已提交，2:已发送")
    private Integer status;

    @ApiModelProperty("推送状态 1:已推送 2推送异常 3线下提交")
    private Integer access;

    @ApiModelProperty("文件url")
    private String fileUrl;

    @ApiModelProperty("邮箱")
    private String mail;

    @ApiModelProperty("创建人操作人身份标识")
    private String createUser;

    @ApiModelProperty("修改人操作人身份标识")
    private String updateUser;

    @ApiModelProperty("是否删除：1已删除 0未删除")
    private Integer isDeleted;

    @ApiModelProperty("资方")
    @DataPermission(type = DataPermissionType.MASK_MONEY)
    private String capitalPool;

    @ApiModelProperty("本金")
    private BigDecimal prinAmt;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "还/退款时间")
    private LocalDateTime repayRefundTime;
}
