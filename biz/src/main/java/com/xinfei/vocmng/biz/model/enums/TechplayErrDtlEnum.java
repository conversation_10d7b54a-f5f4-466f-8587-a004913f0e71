/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

/**
 * 错误码明显段定义枚举
 *
 * <p>本枚举的code对应于标准错误码10~12位。
 * 而errorLevel对应于标准错误码的第4位
 *
 * <p>在标准错误码的位置如下：
 *     <table border="1">
 *     <tr>
 *     <td>位置</td><td>1</td><td>2</td><td>3</td><td bgcolor="yellow">4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td bgcolor="red">10</td><td bgcolor="red">11</td><td bgcolor="red">12</td>
 *     </tr>
 *     <tr>
 *     <td>示例</td><td>X</td><td>E</td><td>0</td><td>1</td><td>0</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>2</td><td>7</td>
 *     </tr>
 *     <tr>
 *     <td>说明</td><td colspan=2>固定<br>标识</td><td>规<br>范<br>版<br>本</td><td>错<br>误<br>级<br>别</td><td>错<br>误<br>类<br>型</td><td colspan=4>错误场景</td><td colspan=3>错误编<br>码</td>
 *     </tr>
 *     </table>
 *
 * <p>错误明细码的CODE取值空间如下：
 * <ul>
 *     <li>公共类错误码[000-099,999]
 *     <li>事务管理类错误码[100-149]
 *     <li>支用还款等交易类错误码[150-245]
 *     <li>日终处理类错误码[250-299]
 *     <li>查询类错误码[300-349]
 *     <li>管理类错误码[350-399]
 * </ul>
 *
 * <AUTHOR>
 * @version $ TechplayErrDtlEnum, v 0.1 2023/8/28 17:42 Jinyan.Huang Exp $
 */
public enum TechplayErrDtlEnum {

    //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\//
    //                      公共类错误码[000-099,999]                             //
    //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\//
    /**
     * 其它未知异常
     */
    UNKNOWN_EXCEPTION("999", ErrorLevelsEnum.ERROR, "其它未知异常"),

    /**
     * 配置错误
     */
    CONFIGURATION_ERROR("001", ErrorLevelsEnum.FATAL, "配置错误"),

    /**
     * 数据库异常
     */
    DB_EXCEPTION("002", ErrorLevelsEnum.ERROR, "数据库异常"),

    /**
     * 数据更新异常
     */
    DATA_UPDATE_EXCEPTION("003", ErrorLevelsEnum.ERROR, "数据库异常"),

    //========================================================================//
    //                              业务处理类                                  //
    //========================================================================//
    /**
     * 服务调用请求信息不合法
     */
    REQ_PARAM_NOT_VALID("101", ErrorLevelsEnum.WARN, "服务调用请求信息不合法"),
    CLIENT_CODE_ERROR("102", ErrorLevelsEnum.ERROR, "客户端返回状态异常-"),
    CLIENT_SYS_ERROR("103", ErrorLevelsEnum.ERROR, "当前人数较多,请稍后再试"),
    REQUEST_PARAM_ERROR("207", ErrorLevelsEnum.WARN, "请求参数有误"),
    LOGIN_ERROR("208", ErrorLevelsEnum.ERROR, "获取验证码异常"),
    NOLOGIN_ERROR("209", ErrorLevelsEnum.WARN, "未登录或登录已过期"),
    LOGINCODE_ERROR("210", ErrorLevelsEnum.WARN, "验证码错误"),
    USERCLOSE_ERROR("211", ErrorLevelsEnum.WARN, "用户账号已关闭"),
    DINGTALK_TOKEN_ERROR("212", ErrorLevelsEnum.WARN, "获取钉钉access token异常"),
    DINGTALK_GETUSER_ERROR("213", ErrorLevelsEnum.WARN, "获取钉钉用户信息失败"),
    DINGTALK_MOBILE_ERROR("214", ErrorLevelsEnum.WARN, "获取钉钉对应电话为空"),
    NOUSER_ERROR("215", ErrorLevelsEnum.WARN, "用户不存在"),
    LOGIN_NOTOKEN_ERROR("216", ErrorLevelsEnum.WARN, "token为空"),
    LOGIN_TOKEN_VAIN_ERROR("217", ErrorLevelsEnum.WARN, "token无效"),
    LOGIN_NATIVE_CACHE_ERROR("218", ErrorLevelsEnum.WARN, "本地缓存用户为空"),
    RESOURCE_PERMISSION_ERROR("219", ErrorLevelsEnum.WARN, "权限注解使用错误"),
    NO_RESOURCE_PERMISSION_ERROR("220", ErrorLevelsEnum.WARN, "您无权限"),
    DATA_PARSE_ERROR("221", ErrorLevelsEnum.WARN, "数据权限转换异常"),
    NO_SUMMARY_ERROR("222", ErrorLevelsEnum.ERROR, "没有小结id"),
    NO_CUSTNO_ERROR("223", ErrorLevelsEnum.ERROR, "该userno未获取到有效custno"),
    EXIST_DEPARTMENT_ERROR("224", ErrorLevelsEnum.ERROR, "组织名已存在"),
    NO_DEPARTMENT_ERROR("225", ErrorLevelsEnum.ERROR, "组织名为空"),
    NO_DEPARTMENTID_ERROR("226", ErrorLevelsEnum.ERROR, "id为空"),
    HAD_DEPARTMENTID_ERROR("227", ErrorLevelsEnum.ERROR, "该组织或下级组织还有员工，不能删除"),
    NO_ROLE_NAME_ERROR("228", ErrorLevelsEnum.ERROR, "角色名字或描述为空"),
    HAS_ROLE_NAME_ERROR("229", ErrorLevelsEnum.ERROR, "角色名字已存在"),
    ROLE_NAME_ERROR("230", ErrorLevelsEnum.ERROR, "角色新增失败"),
    ROLE_EFFECTIVE_ERROR("231", ErrorLevelsEnum.ERROR, "该角色已关联用户，删除失败"),
    FIELD_ERROR("232", ErrorLevelsEnum.ERROR, "数据缺失，必传字段为空"),
    USER_EXISTS_ERROR("233", ErrorLevelsEnum.ERROR, "用户名和手机号已存在"),
    LABEL_EXISTS_ERROR("234", ErrorLevelsEnum.ERROR, "该标签类型名字已存在"),
    LABEL_NAME_EXISTS_ERROR("235", ErrorLevelsEnum.ERROR, "该标签名字已存在"),
    TOO_MANY_DATA_ERROR("236", ErrorLevelsEnum.ERROR, "导入数据量太大，请分多次导入"),
    NODATA_ERROR("237", ErrorLevelsEnum.ERROR, "导入数据为空"),
    IMP_DATA_ERROR("238", ErrorLevelsEnum.ERROR, "导入数据异常"),
    NO_DEPARTMENT_NAME_ERROR("239", ErrorLevelsEnum.ERROR, "该组织名已存在"),
    MOBILE_LABEL_ERROR("240", ErrorLevelsEnum.ERROR, "手机号和标签已存在关联"),
    NAME_LENGTH_ERROR("241", ErrorLevelsEnum.ERROR, "姓名长度过大"),
    USER_LABEL_DELETED_ERROR("242", ErrorLevelsEnum.ERROR, "删除标签关系失败"),
    LABEL_NAME_LENGTH_ERROR("243", ErrorLevelsEnum.ERROR, "标签名长度1-10"),
    DATABASE_ERROR("244", ErrorLevelsEnum.ERROR, "数据异常"),
    USER_LABEL_ERROR("245", ErrorLevelsEnum.ERROR, "手机号打标错误"),
    LOAN_FLAG_ERROR("246", ErrorLevelsEnum.ERROR, "该订单所有账单都已结清"),
    EXEMPTION_ERROR("247", ErrorLevelsEnum.ERROR, "减免计算错误"),
    FEE_LIMIT_ERROR("248", ErrorLevelsEnum.ERROR, "费控计算错误"),
    FEE_SHOW_ERROR("249", ErrorLevelsEnum.ERROR, "费项展示错误"),
    DATA_EXISTS_ERROR("250", ErrorLevelsEnum.ERROR, "数据重复"),
    REPAY_PLAN_ERROR("251", ErrorLevelsEnum.ERROR, "还款方案错误"),
    REPAY_ERROR("252", ErrorLevelsEnum.ERROR, "还款错误"),
    DEDUCTION_ERROR("253", ErrorLevelsEnum.ERROR, "抵扣错误"),
    REFUND_ERROR("254", ErrorLevelsEnum.ERROR, "退款错误"),

    SUMMARY_ERROR("255", ErrorLevelsEnum.ERROR, "小结迁移错误"),
    SSO_LOGIN_ERROR("266", ErrorLevelsEnum.ERROR, "sso登录异常"),
    SSO_REGISTER_ERROR("267", ErrorLevelsEnum.ERROR, "sso注册异常"),
    USER_LOG_OFF_CHECK_ERROR("256", ErrorLevelsEnum.ERROR, "客户注销校验失败"),

    UPDATE_ERROR("257", ErrorLevelsEnum.ERROR, "数据修改错误"),
    VIP_CARD_ERROR("258", ErrorLevelsEnum.ERROR, "会员卡异常"),
    UDESK_ERROR("259", ErrorLevelsEnum.ERROR, "小结弹屏异常")
    ;

    // ~~~ 属性定义 ~~~

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 错误级别
     */
    private final ErrorLevelsEnum errorLevel;

    /**
     * 描述说明
     */
    private final String description;

    /**
     * 私有构造函数。
     *
     * @param code        枚举编码
     * @param errorLevel  错误级别
     * @param description 描述说明
     */
    private TechplayErrDtlEnum(String code, ErrorLevelsEnum errorLevel, String description) {
        this.code = code;
        this.errorLevel = errorLevel;
        this.description = description;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code 枚举编码
     * @return 支付错误明细枚举
     */
    public static TechplayErrDtlEnum getByCode(String code) {
        for (TechplayErrDtlEnum detailCode : values()) {
            if (detailCode.getCode().equals(code)) {

                return detailCode;
            }
        }
        return null;
    }

    // ~~~容器方法 ~~~

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return description;
    }

    /**
     * Getter method for property <tt>errorLevvel</tt>.
     *
     * @return property value of errorLevvel
     */
    public ErrorLevelsEnum getErrorLevel() {
        return errorLevel;
    }
}
