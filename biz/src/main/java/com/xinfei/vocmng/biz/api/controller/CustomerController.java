/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api.controller;

import cn.hutool.crypto.digest.DigestUtil;
import com.xinfei.vocmng.biz.api.CustomerApi;
import com.xinfei.vocmng.biz.config.SmsTemplate;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.OperateType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryReq;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.remote.NPayService;
import com.xinfei.vocmng.biz.remote.WorkOrderRemoteService;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.CommunicateSummaryService;
import com.xinfei.vocmng.biz.service.CouponService;
import com.xinfei.vocmng.biz.service.CustomerService;
import com.xinfei.vocmng.itl.client.feign.impl.AppConfigService;
import com.xinfei.vocmng.itl.rr.AppConfigRequest;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import com.xinfei.vocmng.itl.rr.dto.AppConfigDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDetailDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CustomerController, v 0.1 2023-12-19 15:13 junjie.yan Exp $
 */
@RestController
@LoginRequired
public class CustomerController implements CustomerApi {

    @Resource
    private CustomerService customerService;

    @Resource
    private AppConfigService appConfigService;

    @Resource
    private CouponService couponService;

    @Resource
    private NPayService nPayService;

    @Resource
    private WorkOrderRemoteService workOrderRemoteService;

    @Resource
    private CommunicateSummaryService communicateSummaryService;

    @Override
    @DigestLogAnnotated("getCustomerList")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<Paging<CustomerDto>> getCustomerList(GetCustomerListRequest request) {
        if (StringUtils.isEmpty(request.getMobile()) && StringUtils.isEmpty(request.getIdCard()) && StringUtils.isEmpty(request.getMobileCust()) && StringUtils.isEmpty(request.getUserNo())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "手机号,身份证号,userNo必填其一");
        }
        return ApiResponse.success(customerService.getCustomerList(request));
    }

    @Override
    @DigestLogAnnotated("getUserNoList")
    public ApiResponse<List<UserNoAppDto>> getUserNoList(GetUserNoListRequest request) {
        if (StringUtils.isEmpty(request.getMobile()) && StringUtils.isEmpty(request.getIdCard())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "手机号或者身份证号必填其一");
        }
        return ApiResponse.success(customerService.getUserNoList(request));
    }

    private List<UserNoAppDto> appSort(GetUserNoListRequest request, List<UserNoAppDto> userNoAppDtos) {
        if (CollectionUtils.isEmpty(userNoAppDtos) || userNoAppDtos.size() == 1) {
            return userNoAppDtos;
        }

        if (StringUtils.isNotEmpty(request.getRegisterApp())) {
            List<UserNoAppDto> appDtos = userNoAppDtos.stream().filter(r -> r.getApp().equals(request.getRegisterApp())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(appDtos) && appDtos.size() == 1) {
                List<UserNoAppDto> newAppDtos = new ArrayList<>();
                newAppDtos.add(appDtos.get(0));
                newAppDtos.addAll(userNoAppDtos.stream().filter(r -> !r.getApp().equals(request.getRegisterApp())).collect(Collectors.toList()));
                return newAppDtos;
            } else if (CollectionUtils.isNotEmpty(appDtos) && appDtos.size() > 1) {
                if (StringUtils.isNotEmpty(request.getMobile())) {
                    List<UserNoAppDto> mobileDtos = appDtos.stream().filter(r -> r.getMobile().equals(request.getMobile())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(mobileDtos)) {
                        List<UserNoAppDto> newAppDtos = new ArrayList<>();
                        newAppDtos.add(mobileDtos.get(0));
                        newAppDtos.addAll(appDtos.stream().filter(r -> !r.getMobile().equals(request.getMobile())).collect(Collectors.toList()));
                        newAppDtos.addAll(userNoAppDtos.stream().filter(r -> !r.getApp().equals(request.getRegisterApp())).collect(Collectors.toList()));
                        return newAppDtos;
                    }
                }
            }
        }

        return userNoAppDtos;
    }

    @Override
    @DigestLogAnnotated("getCustomer")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<CustomerDetailDto> getCustomer(GetCustomerRequest request) {
        if (StringUtils.isEmpty(request.getUserNo())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "userNo未传");
        }
        return ApiResponse.success(customerService.getCustomerDetail(request));
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<List<BankDto>> getAllBankCardList(GetAllBankCardListReq request) {
        return ApiResponse.success(customerService.getAllBankCardList(request.getUserNo()));
    }

    @Override
    @DigestLogAnnotated("getCustomerImages")
    public ApiResponse<ImagesDto> getCustomerImages(GetImagesRequest request) {
        if (StringUtils.isEmpty(request.getCustNo())) {
            ImagesDto imagesDto = new ImagesDto();
            return ApiResponse.success(imagesDto);
        }

        return ApiResponse.success(customerService.getImagesDetail(request.getUserNo(), request.getCustNo()));
    }

    @Override
    @DigestLogAnnotated("sendMessage")
    public ApiResponse<Boolean> sendMessage(SendMessageRequest request) {
        return ApiResponse.success(customerService.sendMessage(request));
    }

    @Override
    @DigestLogAnnotated("getMessageTemplates")
    public ApiResponse<List<SmsTemplate>> getMessageTemplates() {
        return ApiResponse.success(customerService.getSmsTemplates());
    }

    @Override
    @DigestLogAnnotated("getAppConfigs")
    public ApiResponse<List<AppConfigDto>> getAppConfigs() {
        //app_id=vocmng&parans1=app&ts=1705319027u5cbLC7f2lhmngdf
        String appId = "vocmng";
        String appIdKey = "u5cbLC7f2lhmngdf";
        String parans1 = "app";
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        String str = "app_id=" + appId + "&parans1=" + parans1 + "&ts=" + ts + appIdKey;
        String md5Pwd = DigestUtil.md5Hex(str);

        AppConfigRequest request = new AppConfigRequest();
        request.setAppId(appId);
        request.setTs(ts);
        request.setParans1(parans1);
        request.setSign(md5Pwd);
        return ApiResponse.success(appConfigService.getAllApp(request));
    }

    @Override
    @DigestLogAnnotated("userLogOff")
    public ApiResponse<Boolean> userLogOff(LogOffReq logOffReq) {
        if (StringUtils.isEmpty(logOffReq.getUserNo()) || StringUtils.isEmpty(logOffReq.getRemark()) || logOffReq.getImmediate() == null) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "userNo或注销原因,immediate注销类型未传");
        }
        return ApiResponse.success(customerService.userLogOff(logOffReq));

    }

    @Override
    @DigestLogAnnotated("userLogOffCancel")
    public ApiResponse<Boolean> userLogOffCancel(LogOffReq logOffReq) {
        if (StringUtils.isEmpty(logOffReq.getUserNo())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "userNo未传");
        }
        return ApiResponse.success(customerService.userLogOffCancel(logOffReq));
    }

    @Override
    @DigestLogAnnotated("getUserCoupon")
    public ApiResponse<Paging<UserCouponDto>> getUserCoupon(UserCouponReq userCouponReq) {
        return couponService.getUserCoupon(userCouponReq);
    }

    @Override
    @DigestLogAnnotated("getUserCouponDetail")
    public ApiResponse<List<UserCouponDetailDto>> getUserCouponDetail(UserCouponDetailReq userCouponDetailReq) {
        return couponService.getUserCouponDetail(userCouponDetailReq);
    }

    @Override
    @DigestLogAnnotated("repayListQuery")
    public ApiResponse<Boolean> repayListQuery(RepayListReq request) {
        return ApiResponse.success(customerService.repayListQuery(request.getCustNo()));
    }

    @Override
    @DigestLogAnnotated("repayListUpdate")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "多卡轮扣新增修改")
    public ApiResponse<Boolean> repayListUpdate(RepayListCreateReq request) {
        return ApiResponse.success(customerService.repayListUpdate(request));
    }

    @Override
    public ApiResponse<PublicAccountInfo> queryPublicAccountInfo(PublicAccountRequest request) {
        return ApiResponse.success(nPayService.queryPublicAccountInfo(request));
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<PageResultResponse<WorkOrderDetailDto>> queryWorkOrderList(QueryWorkOrderMobileRequest request) {
        return ApiResponse.success(workOrderRemoteService.queryWorkMobileDetail(request));
    }

    @Override
    public ApiResponse<PageResultResponse<CommunicateSummaryResp>> queryCommunicateSummaryList(CommunicateSummaryReq communicateSummaryReq) {
        return ApiResponse.success(communicateSummaryService.list(communicateSummaryReq));
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<List<UserRelateNumber> > queryUserRelateNumber(QueryUserRelateNumberRequest queryUserRelateNumberRequest) {
        return ApiResponse.success(customerService.queryUserRelateNumber(queryUserRelateNumberRequest));
    }
}