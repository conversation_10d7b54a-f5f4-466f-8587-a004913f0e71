package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.req.GetSummaryLogsReq;
import com.xinfei.vocmng.biz.model.req.IdCardVerifyRequest;
import com.xinfei.vocmng.biz.model.req.UserLabelRequest;
import com.xinfei.vocmng.biz.model.req.UserNosRequest;
import com.xinfei.vocmng.biz.model.req.UserStatusRequest;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.IVRLatestBillDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLoanSettlementDto;
import com.xinfei.vocmng.biz.rr.dto.IVRMonthlyAmountDto;
import com.xinfei.vocmng.biz.rr.dto.IVROrderInfoDto;
import com.xinfei.vocmng.biz.rr.dto.UserNoAppDto;
import com.xinfei.vocmng.biz.rr.request.GetUserNoListRequest;
import com.xinfei.vocmng.biz.rr.request.UdeskSendMessageRequest;
import com.xinfei.vocmng.biz.service.CustomerService;
import com.xinfei.vocmng.biz.service.LoanService;
import com.xinfei.vocmng.biz.service.impl.VocmngService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.xml.bind.ValidationException;
import java.util.List;

@Api(tags = "vocmng")
@RestController
@RequestMapping("/vocmng")
public class VocprodApi {

    @Autowired
    private VocmngService vocmngService;

    @Autowired
    private LoanService loanService;

    @Autowired
    private CustomerService customerService;

    @ApiOperation("近7天会话小结数量")
    @PostMapping("/summaryLogs")
    public ApiResponse<Integer> getSummaryLogs(@RequestBody GetSummaryLogsReq req) {
        return ApiResponse.success(vocmngService.getSummaryLogs(req.getUserNo()));
    }

    @ApiOperation("用户标签")
    @PostMapping("/getUserLabel")
    public ApiResponse<String> getUserLabel(@RequestBody UserLabelRequest req) throws ValidationException {
        return ApiResponse.success(vocmngService.getUserLabel(req.getUserNo()));
    }

    @ApiOperation("是否是正常用户")
    @PostMapping("/getUserStatus")
    public ApiResponse<Boolean> getUserStatus(@RequestBody UserStatusRequest req) {
        return ApiResponse.success(vocmngService.getUserStatus(req.getMobile()));
    }

    @ApiOperation("ivr账单查询")
    @PostMapping("/queryIVROrderList")
    public ApiResponse<IVROrderInfoDto> queryIVROrderList(@RequestBody UserNosRequest req) {
        return ApiResponse.success(loanService.queryIVROrderList(req.getCustomNo(), req.getMobile()));
    }

    @ApiOperation("ivr近期账单查询")
    @PostMapping("/queryIVRLatestBill")
    public ApiResponse<IVRLatestBillDto> queryIVRLatestBill(@RequestBody UserNosRequest req) {
        return ApiResponse.success(loanService.queryIVRLatestBill(req.getCustomNo(), req.getMobile()));
    }

    @ApiOperation("ivr当月应还金额查询")
    @PostMapping("/queryIVRMonthlyAmount")
    public ApiResponse<IVRMonthlyAmountDto> queryIVRMonthlyAmount(@RequestBody UserNosRequest req) {
        return ApiResponse.success(loanService.queryIVRMonthlyAmount(req.getCustomNo(), req.getMobile()));
    }

    @ApiOperation("ivr在贷未结清订单查询")
    @PostMapping("/queryIVRLoanSettlement")
    public ApiResponse<IVRLoanSettlementDto> queryIVRLoanSettlement(@RequestBody UserNosRequest req) {
        return ApiResponse.success(loanService.queryIVRLoanSettlement(req.getCustomNo(), req.getMobile()));
    }

    @ApiOperation("验证手机号和身份证后6位是否匹配")
    @PostMapping("/verifyIdCardLast6")
    public ApiResponse<Boolean> verifyIdCardLast6(@RequestBody IdCardVerifyRequest req) {
        return ApiResponse.success(vocmngService.verifyIdCardLast6(req.getMobile(), req.getIdCardLast6()));
    }

    @ApiOperation("查询用户userNo")
    @PostMapping("/getUserNoList")
    public ApiResponse<List<UserNoAppDto>> getUserNoList(@RequestBody GetUserNoListRequest req) {
        return ApiResponse.success(customerService.getUserNoList(req));
    }

    @ApiOperation("发送短信")
    @PostMapping("/sendSms")
    public ApiResponse<Void> sendSms(@RequestBody UdeskSendMessageRequest req) {
        vocmngService.sendSms(req);
        return ApiResponse.success();
    }

}
