package com.xinfei.vocmng.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.repaytrade.facade.rr.dto.PlanDetailInfo;
import com.xinfei.repaytrade.facade.rr.request.CreateRepaymentPlanRequest;
import com.xinfei.repaytrade.facade.rr.request.RepayLoanCalcRequest;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.repaytrade.facade.rr.response.RepayLoanCalcResponse;
import com.xinfei.repaytrade.facade.rr.response.RepaymentPlanInvalidResponse;
import com.xinfei.vocmng.biz.constants.ReqNoConstants;
import com.xinfei.vocmng.biz.mapstruct.OfferLockedRateRecordConverter;
import com.xinfei.vocmng.biz.model.enums.*;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.rr.dto.FeeRateViewDto;
import com.xinfei.vocmng.biz.rr.dto.LoanListDto;
import com.xinfei.vocmng.biz.rr.dto.OfferLockedRateRecordDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.OfferInvalidResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.service.OfferManageService;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.biz.strategy.dto.FeeRateLimitStrategyInput;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.OfferLockedRateRecordMapper;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.dal.po.OfferLockedRateRecord;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.util.enums.ErrDtlEnum;
import com.xinfei.vocmng.util.exception.VocmngException;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import io.kyoto.pillar.lcs.loan.domain.response.PlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.xinfei.vocmng.biz.constants.ReqNoConstants.getRequestNo;

/**
 * Offer管理平台Service实现
 *
 * <AUTHOR>
 * @version $ OfferManageServiceImpl, v 0.1 2025-07-21 shaohui.chen Exp $
 */
@Service
@Slf4j
public class OfferManageServiceImpl implements OfferManageService {

    @Resource
    private LcsFeignService lcsFeignService;

    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private FeeRateLimitServiceImpl feeRateLimitService;

    @Resource
    private UserLabelService userLabelService;

    @Resource
    private OfferLockedRateRecordMapper offerLockedRateRecordMapper;

    @Override
    public List<LoanListDto> queryLoanList(LoanListRequest request) {
        if (request.getLoanOrderNos() == null || request.getLoanOrderNos().isEmpty()) {
            return Collections.emptyList();
        }

        List<LoanListDto> result = new ArrayList<>();

        // 批量查询LCS借据信息
        List<String> loanNos = new ArrayList<>(request.getLoanOrderNos().keySet());
        List<LoanPlanResponse> lcsResponses = queryLcsLoanInfo(loanNos);

        // 批量查询订单信息
        List<String> orderNos = new ArrayList<>(request.getLoanOrderNos().values());
        Map<String, ManageOrderDetailDTO> orderDetailMap = batchQueryOrderDetails(orderNos);

        // 批量查询用户标签
        Map<Long, String> userLabelsMap = batchQueryUserLabels(orderDetailMap);

        for (Map.Entry<String, String> entry : request.getLoanOrderNos().entrySet()) {
            String loanNo = entry.getKey();
            String orderNo = entry.getValue();
            LoanListDto dto = new LoanListDto();
            dto.setOrderNo(orderNo);
            dto.setLoanNo(loanNo);

            // 查找对应的LCS数据
            LoanPlanResponse lcsResponse = lcsResponses.stream()
                    .filter(response -> Objects.equals(loanNo, response.getLoanNo()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(lcsResponse)) {
                // 计算已还总额和剩余应还
                calculatePaidAndRemaining(dto, lcsResponse);
                // 计算费率信息
                calculateFeeRate(dto, loanNo);
                // 执行费控逻辑，计算最小费率
                ManageOrderDetailDTO orderDetail = orderDetailMap.get(orderNo);
                if (Objects.nonNull(orderDetail)) {
                    String userLabels = userLabelsMap.get(orderDetail.getUserNo());
                    calculateMinFeeRate(dto, loanNo, orderDetail, lcsResponse, userLabels);
                }
            }

            result.add(dto);
        }

        return result;
    }

    /**
     * 批量查询订单详情信息
     */
    private Map<String, ManageOrderDetailDTO> batchQueryOrderDetails(List<String> orderNos) {
        Map<String, ManageOrderDetailDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(orderNos)) {
            return result;
        }

        try {
            ManageOrderListRequest request = new ManageOrderListRequest();
            request.setOrderNos(orderNos);
            request.setPageSize(orderNos.size());
            request.setPageNo(1);

            List<ManageOrderDetailDTO> orderDetails = lendQueryFacadeClient.getOrderList(request).getPageList();
            if (CollectionUtils.isNotEmpty(orderDetails)) {
                for (ManageOrderDetailDTO orderDetail : orderDetails) {
                    result.put(orderDetail.getLoanReqNo(), orderDetail);
                }
            }
        } catch (Exception e) {
            log.error("批量查询订单详情失败 - orderNos: {}", orderNos, e);
        }

        return result;
    }

    /**
     * 批量查询用户标签
     */
    private Map<Long, String> batchQueryUserLabels(Map<String, ManageOrderDetailDTO> orderDetailMap) {
        Map<Long, String> result = new HashMap<>();
        if (orderDetailMap.isEmpty()) {
            return result;
        }
        try {
            // 提取所有用户号，去重
            Set<Long> userNos = orderDetailMap.values().stream()
                    .map(ManageOrderDetailDTO::getUserNo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            if (userNos.isEmpty()) {
                return result;
            }
            // 批量查询用户标签
            for (Long userNo : userNos) {
                try {
                    List<LabelDto> labelDtos = userLabelService.getUserLabel(String.valueOf(userNo));
                    if (CollectionUtils.isNotEmpty(labelDtos)) {
                        String userLabels = labelDtos.stream()
                                .map(dto -> "[" + dto.getName() + "]")
                                .collect(Collectors.joining());
                        result.put(userNo, userLabels);
                    }
                } catch (Exception e) {
                    log.warn("查询用户标签失败 - userNo: {}, 错误: {}", userNo, e.getMessage());
                }
            }
            log.info("批量查询用户标签完成，用户数量: {}, 成功查询: {}", userNos.size(), result.size());
        } catch (Exception e) {
            log.error("批量查询用户标签异常", e);
        }
        return result;
    }

    /**
     * 计算最小费率
     */
    private void calculateMinFeeRate(LoanListDto dto, String loanNo, ManageOrderDetailDTO orderDetail, LoanPlanResponse lcsResponse, String userLabels) {
        try {
            // 构建费控策略输入参数
            FeeRateLimitStrategyInput strategyInput = FeeRateLimitStrategyInput.builder()
                    .loanNo(loanNo)
                    .orderDetailDTO(orderDetail)
                    .loanPlanResponse(lcsResponse)
                    .userLabels(userLabels)
                    .build();

            // 执行费控策略
            StrategyExecutionResult<FeeStrategyConfig> strategyResult =
                    feeRateLimitService.executeStrategy(FeeStrategyEnum.FEE_RATE_LIMIT_REDUCTIONS, strategyInput);

            // 从策略结果中获取最小费率
            Map<String, String> result = strategyResult.getResult();
            if (result != null && result.containsKey("min_fee_rate")) {
                String minFeeRateStr = result.get("min_fee_rate");
                if (StringUtils.isNotBlank(minFeeRateStr)) {
                    try {
                        BigDecimal minFeeRate = new BigDecimal(minFeeRateStr);
                        dto.setMinFeeRate(minFeeRate);
                    } catch (NumberFormatException e) {
                        log.warn("最小费率格式转换失败 - loanNo: {}, minFeeRateStr: {}", loanNo, minFeeRateStr);
                    }
                }
            }
        } catch (Exception e) {
            log.error("计算最小费率失败 - loanNo: {}", loanNo, e);
        }
    }

    /**
     * 批量查询LCS借据信息
     */
    private List<LoanPlanResponse> queryLcsLoanInfo(List<String> loanNos) {
        try {
            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setLoanNos(loanNos);
            return lcsFeignService.planDetail(loanPlanRequest);
        } catch (Exception e) {
            log.error("查询LCS借据信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 计算已还总额和剩余应还
     */
    private void calculatePaidAndRemaining(LoanListDto dto, LoanPlanResponse lcsResponse) {
        if (Objects.isNull(lcsResponse) || Objects.isNull(lcsResponse.getLoanFeeDetail())) {
            dto.setTotalPaidAmount(BigDecimal.ZERO);
            dto.setRemainingAmount(BigDecimal.ZERO);
            return;
        }
        dto.setTotalPaidAmount(Objects.nonNull(lcsResponse.getLoanFeeDetail().getActRepayPlan()) ?
                lcsResponse.getLoanFeeDetail().getActRepayPlan().getSumAmt() : BigDecimal.ZERO);
        dto.setRemainingAmount(Objects.nonNull(lcsResponse.getLoanFeeDetail().getActShouldRepay()) ?
                lcsResponse.getLoanFeeDetail().getActShouldRepay().getSumAmt() : BigDecimal.ZERO);
    }

    /**
     * 费率信息
     */
    private void calculateFeeRate(LoanListDto dto, String loanNo) {
        // 按照现有rateCalculation接口的方式调用
        RepayLoanCalcResponse response = repayFacadeClient.repayCalculateFeeRatio(
                loanNo,
                RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE,
                null,
                null,
                new BigDecimal(0)
        );

        if (Objects.nonNull(response) && Objects.nonNull(response.getDeductCalculateResponse())) {
            // 设置还款引擎费率
            dto.setActualFeeRate(response.getDeductCalculateResponse().getFeeRatioAfterDeduct());
        }
    }

    @Override
    public Boolean checkFuturePrincipalLessThanDeduction(FuturePrincipalCheckRequest request) {
        try {
            // 按照rateCalculation方法的方式调用还款引擎接口
            BigDecimal targetFeeRatio = null;
            if (request.getTargetFeeRatio() != null) {
                // 将百分比转换为小数，如24% -> 0.24
                targetFeeRatio = request.getTargetFeeRatio().multiply(new BigDecimal("0.01"));
            }

            // 调用还款引擎费率计算接口（只调用一次）
            RepayLoanCalcResponse response = repayFacadeClient.repayCalculateFeeRatio(
                    request.getLoanNo(),
                    RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE, // 结清
                    null, // terms
                    targetFeeRatio, // 目标费率
                    null  // targetDeduct
            );

            if (Objects.isNull(response)) {
                log.warn("还款引擎返回为空, loanNo: {}", request.getLoanNo());
                return false;
            }

            // 1. 获取建议退款金额（减免金额）
            BigDecimal suggestedRefundAmt = BigDecimal.ZERO;
            if (Objects.nonNull(response.getDeductCalculateResponse()) &&
                    Objects.nonNull(response.getDeductCalculateResponse().getSuggestedRefundAmt())) {
                suggestedRefundAmt = response.getDeductCalculateResponse().getSuggestedRefundAmt();
            }

            // 2. 通过LCS接口计算逾期本金+未到期本金
            BigDecimal futurePrincipal = BigDecimal.ZERO;
            try {
                LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
                loanPlanRequest.setLoanNos(Collections.singletonList(request.getLoanNo()));
                List<LoanPlanResponse> lcsResponses = lcsFeignService.planDetail(loanPlanRequest);

                if (CollectionUtils.isNotEmpty(lcsResponses)) {
                    LoanPlanResponse lcsResponse = lcsResponses.get(0);
                    if (lcsResponse != null && CollectionUtils.isNotEmpty(lcsResponse.getPlanList())) {
                        for (io.kyoto.pillar.lcs.loan.domain.response.PlanResponse planResponse : lcsResponse.getPlanList()) {
                            // 计算逾期本金（rpyFlag="1"）+ 未到期本金（rpyFlag="0"）
                            if (!StringUtils.equals(planResponse.getRpyFlag(), "2") &&
                                    Objects.nonNull(planResponse.getPlanFeeDetail()) &&
                                    Objects.nonNull(planResponse.getPlanFeeDetail().getActShouldRepay())
                                    && Objects.nonNull(planResponse.getPlanFeeDetail().getActShouldRepay().getPrinAmt())) {
                                futurePrincipal = futurePrincipal.add(planResponse.getPlanFeeDetail().getActShouldRepay().getPrinAmt());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询LCS账单信息失败, loanNo: {}", request.getLoanNo(), e);
            }

            log.info("未来期本金检查 - loanNo: {}, 建议退款金额: {}, 逾期+未到期本金: {}",
                    request.getLoanNo(), suggestedRefundAmt, futurePrincipal);

            // 3. 判断逾期+未到期本金是否小于减免金额
            return futurePrincipal.compareTo(suggestedRefundAmt) < 0;

        } catch (Exception e) {
            log.error("检查未来期本金失败, loanNo: {}", request.getLoanNo(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public String confirmFeeRate(ConfirmFeeRateRequest request) {
        try {
            log.info("开始确认费率，直接调用还款引擎 - loanFeeRates: {}", request.getLoanFeeRates());

            // 生成批次唯一ID，用于关联同一批次的所有借据
            String batchId = String.valueOf(IdUtil.getSnowflakeNextId());

            // 批量查询订单信息，用于获取订单号
            List<String> loanNos = new ArrayList<>(request.getLoanFeeRates().keySet());
            Map<String, ManageOrderDetailDTO> loanOrderMap = batchQueryOrderDetailsByLoanNos(loanNos);

            // 构建PlanDetailInfo列表用于调用还款引擎
            List<PlanDetailInfo> planDetailInfos = new ArrayList<>();
            int sequence = 1; // 序号，用于区分同批次内的不同借据
            for (Map.Entry<String, BigDecimal> entry : request.getLoanFeeRates().entrySet()) {
                String loanNo = entry.getKey();
                BigDecimal feeRate = entry.getValue();

                // 构建PlanDetailInfo用于调用还款引擎
                PlanDetailInfo planDetailInfo = buildPlanDetailInfo(loanNo, feeRate);
                // 设置有关联性的唯一ID：批次ID_序号
                planDetailInfo.setPlanDetailId(batchId + "_" + String.format("%03d", sequence));
                planDetailInfos.add(planDetailInfo);

                log.info("准备借据费率信息 - loanNo: {}, feeRate: {}%, planDetailId: {}",
                        loanNo, feeRate, planDetailInfo.getPlanDetailId());
                sequence++;
            }

            // 直接调用还款引擎创建固定费率减免
            CreateRepaymentPlanRequest createRepaymentPlanRequest = new CreateRepaymentPlanRequest();
            // 使用批次ID作为请求号的一部分
            createRepaymentPlanRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCCRP, batchId + "_BATCH"));
            repayFacadeClient.createRepaymentPlan(createRepaymentPlanRequest, planDetailInfos, UserContextHolder.getName(), null, null, null);
            log.info("调用还款引擎创建固定费率减免成功 - , batchId: {}", batchId);

            // 插入费率应用记录
            insertFeeRateApplyRecords(request.getLoanFeeRates(), loanOrderMap, planDetailInfos);

            return batchId;
        } catch (Exception e) {
            log.error("确认费率失败 - loanFeeRates: {}", request.getLoanFeeRates(), e);
            throw new VocmngException(ErrDtlEnum.REPAYMENT_PLAN_CREATE_ERROR);
        }
    }


    /**
     * 构建PlanDetailInfo用于调用还款引擎
     */
    private PlanDetailInfo buildPlanDetailInfo(String loanNo, BigDecimal feeRate) {
        PlanDetailInfo planDetailInfo = new PlanDetailInfo();
        planDetailInfo.setDeductChannel(PlanSourceEnum.VOCMNG.getCode());
        planDetailInfo.setSettleType(RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
        planDetailInfo.setLoanNo(loanNo);
        planDetailInfo.setDeductRate(feeRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)); // 转换为小数
        planDetailInfo.setDeductRule(DeductRuleEnum.FEE_RATE_LIMIT.getCode()); // 固定费率减免
        planDetailInfo.setDateExpire(DateUtils.parseDate("2099-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        planDetailInfo.setDateEffective(LocalDateTimeUtils.parseDateByLocalDateTime(LocalDateTime.now()));
        planDetailInfo.setDeductFeeCalcYearDays(360);
        return planDetailInfo;
    }

    @Override
    public List<FeeRateViewDto> queryFeeRateList(FeeRateViewRequest request) {
        log.info("开始查询费率列表 - loanOrderList: {}", request.getLoanOrderList());

        List<FeeRateViewDto> result = new ArrayList<>();

        // 1. 提取借据号列表
        List<String> loanNos = request.getLoanOrderList().stream()
                .map(FeeRateViewRequest.LoanOrderInfo::getLoanNo)
                .collect(Collectors.toList());

        // 2. 批量查询应用费率和方案明细ID
        Map<String, FeeRateInfo> appliedFeeRateMap = queryAppliedFeeRates(loanNos);

        // 3. 批量查询LCS账单信息
        List<LoanPlanResponse> lcsResponses = queryLcsLoanInfo(loanNos);

        // 4. 处理每个借据订单
        for (FeeRateViewRequest.LoanOrderInfo loanOrderInfo : request.getLoanOrderList()) {
            String loanNo = loanOrderInfo.getLoanNo();
            String orderNo = loanOrderInfo.getOrderNo();

            FeeRateViewDto dto = new FeeRateViewDto();
            dto.setOrderNo(orderNo);

            // 设置应用费率和方案明细ID
            FeeRateInfo feeRateInfo = appliedFeeRateMap.get(loanNo);
            if (Objects.nonNull(feeRateInfo)) {
                dto.setAppliedFeeRate(feeRateInfo.getFeeRate());
                dto.setPlanDetailId(feeRateInfo.getPlanDetailId());
            } else {
                dto.setAppliedFeeRate(BigDecimal.ZERO);
                dto.setPlanDetailId(null);
            }

            // 查找对应的LCS数据并计算应还金额
            lcsResponses.stream()
                    .filter(response -> Objects.equals(loanNo, response.getLoanNo()))
                    .findFirst().ifPresent(lcsResponse -> calculatePlanAmounts(dto, lcsResponse));

            result.add(dto);
            log.info("处理借据完成 - loanNo: {}, orderNo: {}, appliedFeeRate: {}%, planDetailId: {}, 优先逾期或最近期未还金额: {}",
                    loanNo, orderNo, dto.getAppliedFeeRate(), dto.getPlanDetailId(), dto.getNearestPlanAmount());
        }

        log.info("费率列表查询完成，共处理{}条记录", result.size());
        return result;
    }

    @Override
    @Transactional
    public OfferInvalidResponse feeRateInvalid(String planDetailId) {
        LambdaQueryWrapper<OfferLockedRateRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfferLockedRateRecord::getPlanDetailId, planDetailId)
                .eq(OfferLockedRateRecord::getIsDel, 0);
        List<OfferLockedRateRecord> offerLockedRateRecords = offerLockedRateRecordMapper.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(offerLockedRateRecords)) {
            OfferLockedRateRecord offerLockedRateRecord = offerLockedRateRecords.get(0);
            offerLockedRateRecord.setId(null);
            offerLockedRateRecord.setUpdater(UserContextHolder.getName());
            offerLockedRateRecord.setCreator(UserContextHolder.getName());
            offerLockedRateRecord.setOperation(OfferRateOperationEnum.INVALID.getCode());
            offerLockedRateRecordMapper.insert(offerLockedRateRecord);
            RepaymentPlanInvalidResponse response = repayFacadeClient.repaymentPlanInvalid(Collections.singletonList(planDetailId), null, "客服主动失效");
            OfferInvalidResponse invalidResponse = new OfferInvalidResponse();
            if (response != null) {
                invalidResponse.setIsSuccess(response.getResult());
                invalidResponse.setMessage(response.getMessage());
            }
            return invalidResponse;
        } else {
            log.warn("未找到对应的方案明细ID - planDetailId: {}", planDetailId);
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "未找到对应的方案明细ID，失效失败");
        }
    }

    @Override
    public Paging<OfferLockedRateRecordDto> getFeeRateRecords(FeeRateRecordReq req) {
        Paging<OfferLockedRateRecordDto> paging = new Paging<>();

        IPage<OfferLockedRateRecord> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<OfferLockedRateRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfferLockedRateRecord::getIsDel, 0);
        wrapper.in(OfferLockedRateRecord::getLoanNo, req.getLoanNos());
        wrapper.orderByDesc(OfferLockedRateRecord::getCreatedTime);
        IPage<OfferLockedRateRecord> pageList = offerLockedRateRecordMapper.selectPage(page, wrapper);
        paging.setTotalPage(pageList.getPages());
        paging.setTotal(pageList.getTotal());
        paging.setList(OfferLockedRateRecordConverter.INSTANCE.convert(pageList.getRecords()));
        paging.setCurrentPage(req.getCurrentPage());
        return paging;
    }

    /**
     * 批量查询应用费率和方案明细ID
     */
    private Map<String, FeeRateInfo> queryAppliedFeeRates(List<String> loanNos) {
        Map<String, FeeRateInfo> result = new HashMap<>();
        try {
            QueryRepaymentPlanResponse response = repayFacadeClient.batchQueryRepaymentPlan(loanNos, 2);
            if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getPlanDeductList())) {
                // 将小数转换为百分比
                for (QueryRepaymentPlanResponse.PlanDeduct planDeduct : response.getPlanDeductList()) {
                    BigDecimal feeRatePercent = planDeduct.getDeductRate()
                            .multiply(new BigDecimal("100"));
                    FeeRateInfo feeRateInfo = new FeeRateInfo();
                    feeRateInfo.setFeeRate(feeRatePercent);
                    feeRateInfo.setPlanDetailId(planDeduct.getPlanDetailId());
                    result.put(planDeduct.getLoanNo(), feeRateInfo);
                }

            }
        } catch (Exception e) {
            log.error("查询应用费率失败 - loanNos: {}", loanNos, e);
        }

        return result;
    }

    /**
     * 费率信息内部类
     */
    private static class FeeRateInfo {
        private BigDecimal feeRate;
        private String planDetailId;

        public BigDecimal getFeeRate() {
            return feeRate;
        }

        public void setFeeRate(BigDecimal feeRate) {
            this.feeRate = feeRate;
        }

        public String getPlanDetailId() {
            return planDetailId;
        }

        public void setPlanDetailId(String planDetailId) {
            this.planDetailId = planDetailId;
        }
    }

    /**
     * 计算计划应还金额（参考LoanServiceImp的计算逻辑）
     */
    private void calculatePlanAmounts(FeeRateViewDto dto, LoanPlanResponse lcsResponse) {
        BigDecimal nearestPlanAmount = BigDecimal.ZERO;
        BigDecimal totalRemainingAmount = BigDecimal.ZERO;
        BigDecimal totalPaidAmount = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(lcsResponse.getPlanList())) {
            // 找到从逾期开始最开始未还金额，如果没有逾期就是最近期要还的
            PlanResponse earliestOverduePlan = null;
            Date earliestOverdueDate = null;
            PlanResponse earliestUnrepaidPlan = null;
            Date earliestUnrepaidDate = null;

            for (PlanResponse planResponse : lcsResponse.getPlanList()) {
                // 处理未还账单：rpyFlag = "0"（未到期）或 "1"（逾期）
                if ("0".equals(planResponse.getRpyFlag()) || "1".equals(planResponse.getRpyFlag())) {
                    // 累加总剩余应还金额，使用planFeeDetail.actShouldRepay.sumAmt
                    if (planResponse.getPlanFeeDetail() != null &&
                        planResponse.getPlanFeeDetail().getActShouldRepay() != null &&
                        planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt() != null) {
                        totalRemainingAmount = totalRemainingAmount.add(
                            planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt());
                    }

                    // 找最早逾期的账单
                    if ("1".equals(planResponse.getRpyFlag()) && Objects.nonNull(planResponse.getDateDue())) {
                        Date currentDueDate = planResponse.getDateDue();
                        if (Objects.isNull(earliestOverdueDate) || currentDueDate.before(earliestOverdueDate)) {
                            earliestOverdueDate = currentDueDate;
                            earliestOverduePlan = planResponse;
                        }
                    }

                    // 找最近期要还的账单（未到期的最早到期账单）
                    if ("0".equals(planResponse.getRpyFlag()) && Objects.nonNull(planResponse.getDateDue())) {
                        Date currentDueDate = planResponse.getDateDue();
                        if (Objects.isNull(earliestUnrepaidDate) || currentDueDate.before(earliestUnrepaidDate)) {
                            earliestUnrepaidDate = currentDueDate;
                            earliestUnrepaidPlan = planResponse;
                        }
                    }
                }

                // 处理已还账单：rpyFlag = "2"（已还）
                if ("2".equals(planResponse.getRpyFlag())) {
                    // 累加已还总额，使用planFeeDetail.actShouldRepay.sumAmt
                    if (Objects.nonNull(planResponse.getPlanFeeDetail()) &&
                            Objects.nonNull(planResponse.getPlanFeeDetail().getActShouldRepay()) &&
                            Objects.nonNull(planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt())) {
                        totalPaidAmount = totalPaidAmount.add(
                                planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt());
                    }
                }
            }

            // 设置从逾期开始最开始未还金额：优先取逾期账单，如果没有逾期则取最近期要还的
            PlanResponse targetPlan = Objects.nonNull(earliestOverduePlan) ? earliestOverduePlan : earliestUnrepaidPlan;
            if (Objects.nonNull(targetPlan) &&
                    Objects.nonNull(targetPlan.getPlanFeeDetail()) &&
                    Objects.nonNull(targetPlan.getPlanFeeDetail().getActShouldRepay()) &&
                    Objects.nonNull(targetPlan.getPlanFeeDetail().getActShouldRepay().getSumAmt())) {
                nearestPlanAmount = targetPlan.getPlanFeeDetail().getActShouldRepay().getSumAmt();
            }
        }

        dto.setNearestPlanAmount(nearestPlanAmount);
        dto.setTotalRemainingAmount(totalRemainingAmount);
        dto.setTotalPaidAmount(totalPaidAmount);
    }
}
