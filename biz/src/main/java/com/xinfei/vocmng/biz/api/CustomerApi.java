package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.config.SmsTemplate;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryReq;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import com.xinfei.vocmng.itl.rr.dto.AppConfigDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDetailDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "用户相关接口")
@RequestMapping("/customer")
public interface CustomerApi {
    @ApiOperation("用户列表")
    @PostMapping(value = "/list", headers = {"Content-Type=application/json"})
    ApiResponse<Paging<CustomerDto>> getCustomerList(@RequestBody GetCustomerListRequest request);

    @ApiOperation("UserNo列表")
    @PostMapping(value = "/userNos")
    ApiResponse<List<UserNoAppDto>> getUserNoList(@RequestBody GetUserNoListRequest request);

    @ApiOperation("用户详情")
    @PostMapping(value = "/detail")
    ApiResponse<CustomerDetailDto> getCustomer(@RequestBody GetCustomerRequest request);

    @ApiOperation("银行卡列表")
    @PostMapping(value = "/getBankCards")
    ApiResponse<List<BankDto>> getAllBankCardList(@RequestBody GetAllBankCardListReq request);

    @ApiOperation("用户照片信息")
    @PostMapping(value = "/images")
    ApiResponse<ImagesDto> getCustomerImages(@Validated @RequestBody GetImagesRequest request);

    @ApiOperation("发短信")
    @PostMapping("/sendMessage")
    ApiResponse<Boolean> sendMessage(@RequestBody SendMessageRequest request);

    @ApiOperation("获取短信模板")
    @PostMapping("/getMessageTemplates")
    ApiResponse<List<SmsTemplate>> getMessageTemplates();

    @ApiOperation("获取APP")
    @PostMapping("/getAppConfigs")
    ApiResponse<List<AppConfigDto>> getAppConfigs();

    @ApiOperation("客户注销")
    @PostMapping("/userLogOff")
    ApiResponse<Boolean> userLogOff(@Validated @RequestBody LogOffReq logOffReq);

    @ApiOperation("客户撤销注销")
    @PostMapping("/userLogOffCancel")
    ApiResponse<Boolean> userLogOffCancel(@Validated @RequestBody LogOffReq logOffReq);

    @ApiOperation("客户优惠券列表")
    @PostMapping("/getUserCoupon")
    ApiResponse<Paging<UserCouponDto>> getUserCoupon(@Validated @RequestBody UserCouponReq userCouponReq);

    @ApiOperation("客户优惠券详情")
    @PostMapping("/getUserCouponDetail")
    ApiResponse<List<UserCouponDetailDto>> getUserCouponDetail(@Validated @RequestBody UserCouponDetailReq userCouponDetailReq);

    @ApiOperation("多卡轮扣查询")
    @PostMapping("/repayListQuery")
    ApiResponse<Boolean> repayListQuery(@Validated @RequestBody RepayListReq request);

    @ApiOperation("多卡轮扣开启or关闭")
    @PostMapping("/repayListUpdate")
    ApiResponse<Boolean> repayListUpdate(@Validated @RequestBody RepayListCreateReq request);

    @ApiOperation("对公账户查询")
    @PostMapping(value = "/queryPublicAccountInfo")
    ApiResponse<PublicAccountInfo> queryPublicAccountInfo(@Validated @RequestBody PublicAccountRequest req);

    @ApiOperation("工单查询")
    @PostMapping("/queryWorkOrderList")
    ApiResponse<PageResultResponse<WorkOrderDetailDto>> queryWorkOrderList(@RequestBody @Valid QueryWorkOrderMobileRequest request);

    @ApiOperation("获取小结列表")
    @PostMapping("/queryCommunicateSummaryList")
    ApiResponse<PageResultResponse<CommunicateSummaryResp>> queryCommunicateSummaryList(@RequestBody CommunicateSummaryReq communicateSummaryReq);

    @ApiOperation("获取关联手机号")
    @PostMapping("/queryUserRelateNumber")
    ApiResponse<List<UserRelateNumber>> queryUserRelateNumber(@RequestBody QueryUserRelateNumberRequest queryUserRelateNumberRequest);
}
