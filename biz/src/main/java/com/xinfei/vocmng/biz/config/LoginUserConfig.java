/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import cn.hutool.json.JSONUtil;
import com.ctrip.framework.apollo.ConfigService;
import com.xinfei.vocmng.biz.model.base.CapitalTemplate;
import com.xinfei.vocmng.biz.model.base.MailConfig;
import com.xinfei.vocmng.itl.rr.dto.ContractBaseDataDto;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ LoginUserConfig, v 0.1 2023/12/21 17:24 wancheng.qu Exp $
 */
@Getter
@Component
@RefreshScope
public class LoginUserConfig {


    @Value("${xf.sms-center.url}")
    private String smsUrl;

    @Value("${sms.templateId}")
    private String smsTemplateId;

    /** 登陆是否校验*/
    @Value("${login.check:true}")
    private boolean loginCheck;

    /** 重定向地址 */
    @Value("${dingtalk.url}")
    private String redirectUrl;

    /** 钉钉后台配置的客户端id */
    @Value("${dingtalk.clientId}")
    private String clientId;

    /** 钉钉后台配置的秘钥 */
    @Value("${dingtalk.appSecret}")
    private String appSecret;

    @Value("${work.url}")
    private String workUrl;

    @Value("${repay.pageSize:300}")
    private long repayPageSize;

    /** 是否使用聚合更新*/
    @Value("${repayment.updatebatch:true}")
    private boolean updateBatch;

    @Value("${voc.timeRangeHours}")
    private long timeRangeHours;

    @Value("${voc.loginCount}")
    private int loginCount;

    @Value("${voc.device.timeRangeHours}")
    private long deviceTimeRangeHours;

    @Value("${voc.device.loginCount}")
    private int deviceLoginCount;

    @Value("${voc.openid.timeRangeHours}")
    private long openIdTimeRangeHours;

    @Value("${voc.openid.loginCount}")
    private int openIdLoginCount;

    @Value("${voc.custnoLoginCount:3}")
    private int custNoLoginCount;

    @Value("${voc.custDevice.loginCount:3}")
    private int custNodeviceLoginCount;

    @Value("${voc.custOpenid.loginCount:3}")
    private int custOpenIdLoginCount;

    /** 线下还款短信通知模板*/
    @Value("${sms.offline.repayment.templateId}")
    private String smsOfflineRepaymentTemplateId;

    /** 聚合支付短信通知模板*/
    @Value("${sms.aggregate.payment.templateId}")
    private String smsAggregatePaymentTemplateId;

    public List<String> getWhiteList() {
        final String key = "advice.white";
        String val = ConfigService.getAppConfig().getProperty(key, "[]");
        return JSONUtil.toList(val, String.class);
    }

    public List<String> getAdviceFlag() {
        final String key = "advice.flag";
        String val = ConfigService.getAppConfig().getProperty(key, "['ip','device','openid']");
        return JSONUtil.toList(val, String.class);
    }

    public ContractBaseDataDto getContractBaseDataDto() {
        final String key = "contract.app";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JSONUtil.toBean(val, ContractBaseDataDto.class);
    }

    public MailConfig getMailConfig() {
        final String key = "mail.config";
        String val = ConfigService.getAppConfig().getProperty(key,null);
        return JSONUtil.toBean(val, MailConfig.class);
    }

    @SuppressWarnings("unchecked")
    public CapitalTemplate getCapitalTemplate(String k) {
        final String key = "mail.template";
        String val = ConfigService.getAppConfig().getProperty(key, null);
        if (val == null || val.isEmpty()) {
            return null;
        }
        Map<String, Object> map = JSONUtil.toBean(val, Map.class);
        Object value = map != null ? map.get(k) : null;
        return value != null ? JSONUtil.toBean(value.toString(), CapitalTemplate.class) : null;
    }


    public static void main(String[] args) {
        Map<String, CapitalTemplate> map = new HashMap<>();
        String text ="您好，请帮忙申请用户结清证明，申请信息详情看附件，谢谢！";
        map.put("mz_ajxt_cash",new CapitalTemplate("word",text,true,new CapitalTemplate.Word("结清证明\n" +
                "\n" +
                "借款人 #{name}（身份证号码为#{idNo} ）通过我公司申请贷款#{amount}元，借据号：#{loanNo}，贷款发放日期#{dateCashY}，该借款人已于#{dateSettleY}，结清本笔贷款本息。\n" +
                "\n" +
                "特此证明。\n" +
                "\n" +
                "#{currentDate}"),null));
        map.put("by_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("客户姓名","name"),new CapitalTemplate.Entry("身份证号码","idNo"),new CapitalTemplate.Entry("借据编号","loanNoZ"),new CapitalTemplate.Entry("渠道码","channel"),new CapitalTemplate.Entry("订单号","loanNo")))));
        map.put("mz_bhxt_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("姓名","name"),new CapitalTemplate.Entry("身份证号","idNo"),new CapitalTemplate.Entry("合同号","loanNoZ"),new CapitalTemplate.Entry("结清日期","dateSettleS"),new CapitalTemplate.Entry("贷款金额","amount"),new CapitalTemplate.Entry("起始日","beginTimeS"),new CapitalTemplate.Entry("到期日","endTimeS")))));
        map.put("wsm_yc_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("资产平台编号","asset"),new CapitalTemplate.Entry("商户订单号","loanNo"),new CapitalTemplate.Entry("借款人姓名","name"),new CapitalTemplate.Entry("借款人身份证号","idNo"),new CapitalTemplate.Entry("贷款金额（元）","amount"),new CapitalTemplate.Entry("贷款发放日期","dateCashS"),new CapitalTemplate.Entry("贷款结清日期","dateSettleS")))));
        map.put("jmx_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("申请日期","applyDate"),new CapitalTemplate.Entry("客户姓名","name"),new CapitalTemplate.Entry("身份证号","idNo"),new CapitalTemplate.Entry("合同编号","loanNoZ"),new CapitalTemplate.Entry("借款金额","amount"),new CapitalTemplate.Entry("账单结清时间","dateSettle")))));
        map.put("tb_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("客户姓名","name"),new CapitalTemplate.Entry("身份证号码","idNo"),new CapitalTemplate.Entry("借据编号","loanNoZ"),new CapitalTemplate.Entry("渠道码","channel"),new CapitalTemplate.Entry("订单号","loanNo")))));
        map.put("wsm_dxal_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("资产平台编号","asset"),new CapitalTemplate.Entry("商户订单号","loanNo"),new CapitalTemplate.Entry("借款人姓名","name"),new CapitalTemplate.Entry("借款人身份证号","idNo"),new CapitalTemplate.Entry("贷款金额（元）","amount"),new CapitalTemplate.Entry("贷款发放日期","dateCashS"),new CapitalTemplate.Entry("贷款结清日期","dateSettleS")))));
        map.put("xmxj_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("借款人姓名","name"),new CapitalTemplate.Entry("手机号","tel"),new CapitalTemplate.Entry("借款人身份证号","idNo"),new CapitalTemplate.Entry("贷款金额（元）","amount"),new CapitalTemplate.Entry("贷款发放日期","dateCashS"),new CapitalTemplate.Entry("贷款结清日期","dateCashS")))));
        map.put("xjhh_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("序号","number"),new CapitalTemplate.Entry("登记日期","registerDate"),new CapitalTemplate.Entry("客户姓名","name"),new CapitalTemplate.Entry("身份证号","idNo"),new CapitalTemplate.Entry("贷款金额","amount"),new CapitalTemplate.Entry("贷款状态","status"),new CapitalTemplate.Entry("合作流水号（资方订单编号）","orderNoZ"),new CapitalTemplate.Entry("借据编号/合同编号","loanNoZ"),new CapitalTemplate.Entry("还款起始日期","dateSettleS")))));
        map.put("yhxd_cash",new CapitalTemplate("excel",text,true,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("商户订单号","loanNo"),new CapitalTemplate.Entry("借款人姓名","name"),new CapitalTemplate.Entry("借款人身份证号","idNo"),new CapitalTemplate.Entry("贷款金额（元）","amount"),new CapitalTemplate.Entry("贷款发放日期","dateCashS"),new CapitalTemplate.Entry("贷款结清日期","dateCashS")))));
        map.put("zdxd_cash",new CapitalTemplate("excel","下图为【#{time}】 需要开具结清证明的具体明细，烦请各位同事协助开具并邮件回复开具结果",false,null,new CapitalTemplate.Excel(Arrays.asList(new CapitalTemplate.Entry("订单号","loanNo"),new CapitalTemplate.Entry("身份证号","idNo"),new CapitalTemplate.Entry("用户姓名","name"),new CapitalTemplate.Entry("订单金额","amount"),new CapitalTemplate.Entry("借款时间","beginTimeS"),new CapitalTemplate.Entry("结清日期","dateSettleS")))));
        System.out.println(JSONUtil.toJsonStr(map));

    }

}