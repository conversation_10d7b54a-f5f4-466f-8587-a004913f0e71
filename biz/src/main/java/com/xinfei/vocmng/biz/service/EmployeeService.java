/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.EmployeeRoleMapping;
import com.xinfei.vocmng.dal.po.UserLabelMapping;
import org.apache.commons.lang3.tuple.MutablePair;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ EmployeeService, v 0.1 2023/12/23 20:05 wancheng.qu Exp $
 */

public interface EmployeeService {

    Employee getEffectiveUser(Employee condition);

    String  getUserNameForIdentify(String identify);

    List<String> findNotExistingRoleNames(List<String> roles);

    List<String> findNotExistingDepartmentNames(List<String> departs);

    List<String> findNotExistingMobileNames(List<String> departs);

    void saveData(List<MutablePair<List<EmployeeRoleMapping>, List<Employee>>> resData);

    List<String> findNotExistingLabelType(List<String> strings);

    List<String> findNotExistingLabelName(List<String> strings);

    void saveLabelEmp(List<List<UserLabelMapping>> res);

    /**
     * 查询有效用户身份信息
     *
     * @param identifyList
     * @return
     */
    List<Employee> queryEffectiveUser(List<String> identifyList);

    void updateSsoUserId(String identify, Long ssoUserId);

    String  getUserDepartment(String identify);
}