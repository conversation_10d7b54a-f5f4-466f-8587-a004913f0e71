package com.xinfei.vocmng.biz.api;

import com.xinfei.psenginecore.facade.rr.dto.AdvanceOrderListOuterDTO;
import com.xinfei.repaytrade.facade.vo.repay.RepayCancelVO;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.*;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "还款相关接口")
@RequestMapping("/repay")
@Validated
public interface RepayApi {

    @ApiOperation("还款流程")
    @PostMapping(value = "/process")
    ApiResponse<RepaymentProcessResp> repaymentProcess(@Validated @RequestBody RepaymentProcessReq req);

    @ApiOperation("还款方案保存/app自助还款")
    @PostMapping(value = "/savePlan")
    ApiResponse<RepayPlanRequest> saveRepayPlan(@Validated @RequestBody RepayPlanRequest request);

    @ApiOperation("聚合支付/系统代扣/线下还款")
    @PostMapping(value = "/repayment")
    ApiResponse<Boolean> repay(@Validated @RequestBody RepayPlanRequest request);

    @ApiOperation("费率计算")
    @PostMapping(value = "/rateCalculation")
    ApiResponse<List<FeeRatioProcessResp>> rateCalculation(@Valid @RequestBody List<FeeRatioProcessReq> req);

    @ApiOperation("获取渠道")
    @GetMapping(value = "/getChannels")
    ApiResponse<List<Map<String, Object>>> getChannels();

    @ApiOperation("借据试算")
    @PostMapping(value = "/loanCalculate")
    ApiResponse<List<LoanCalculateDto>> loanCalculate(@Validated @RequestBody LoanCalculateRequest request);

    @ApiOperation("银行流水列表")
    @PostMapping(value = "/bankFlow")
    ApiResponse<PageResultResponse<BankFlow>> bankFlow(@Validated @RequestBody BankFlowRequest request);

    @ApiOperation("线下销账")
    @PostMapping(value = "/writeOff")
    ApiResponse<List<WriteOffResponse>> writeOff(@Validated @RequestBody WriteOffRequest request);

    @ApiOperation("销账记录")
    @PostMapping(value = "/writeOffRecord")
    ApiResponse<List<WriteOffRecord>> writeOffRecord(@Validated @RequestBody WriteOffRecordRequest request);

    @ApiOperation("销账撤销")
    @PostMapping(value = "/writeOffReversal")
    ApiResponse<Boolean> writeOffReversal(@Validated @RequestBody WriteOffReversalRequest request);

    @ApiOperation("还款撤销")
    @PostMapping(value = "/repaymentCancel")
    ApiResponse<List<RepayCancelVO>> repaymentCancel(@Validated @RequestBody WriteOffReversalRequest request);

    @ApiOperation("还款方案列表")
    @PostMapping(value = "/plans")
    ApiResponse<PageResultResponse<RepaymentPlanDto>> repayPlanList(@Validated @RequestBody RepayPlanListRequest request);

    @ApiOperation("审批方案列表")
    @PostMapping(value = "/reviewPlans")
    ApiResponse<PageResultResponse<RepaymentPlanDto>> reviewPlanList(@Validated @RequestBody RepayPlanListRequest request);

    @ApiOperation("还款方案明细")
    @PostMapping(value = "/detail")
    ApiResponse<RepayPlanDetailDto> repayPlanDetail(@Validated @RequestBody PlanDetailRequest request);

    @ApiOperation("催收还款方案明细")
    @PostMapping(value = "/huttaDetail")
    ApiResponse<HuttaPlanDetailDto> huttaDetail(@Validated @RequestBody HuttaDetailRequest request);

    @ApiOperation("还款方案失效")
    @PostMapping(value = "/invalid")
    ApiResponse<List<PlanInvalidResponse>> repayPlanInvalid(@Validated @RequestBody List<PlanInvalidRequest> request);

    @ApiOperation("可抵扣订单")
    @PostMapping(value = "/deductionLoans")
    ApiResponse<List<DeductionLoansResponse>> deductionLoans(@Validated @RequestBody DeductionLoansRequest request);

    @ApiOperation("可抵扣金额范围")
    @PostMapping(value = "/deductionAmt")
    ApiResponse<DeductionAmtResponse> deductionAmt(@Validated @RequestBody DeductionAmtRequest request);

    @ApiOperation("还款方式变更")
    @PostMapping(value = "/updateRepayMethod")
    ApiResponse<Boolean> updateRepayMethod(@Validated @RequestBody RepayMethodRequest request);

    @ApiOperation("抵扣记录")
    @PostMapping(value = "/deductionList")
    ApiResponse<Paging<RepayReductionDto>> deductionList(@Validated @RequestBody RepayTradeDeductionRequest request);

    @ApiOperation("撤销抵扣")
    @PostMapping(value = "/deductionCancel")
    ApiResponse<Boolean> deductionCancel(@Validated @RequestBody RepayTradeDeductionCancelRequest request);

    @ApiOperation("还款减免项计算")
    @PostMapping(value = "/reduceCalculate")
    ApiResponse<ReduceCalculateResponse> reduceCalculate(@Validated @RequestBody ReduceCalculateReq req);

    @ApiOperation("还款减免项计算多条维度")
    @PostMapping(value = "/reduceCalculateMany")
    ApiResponse<List<ReduceCalculateResponse>> reduceCalculateMany(@Validated @RequestBody List<ReduceCalculateReq> request);

    @ApiOperation("还款减免项计算纵向维度")
    @PostMapping(value = "/reduceCalculateVertical")
    ApiResponse<List<ReduceCalculateVerticalResponse>> reduceCalculateVertical(@Validated @RequestBody List<ReduceCalculateVerticalReq> request);

    @ApiOperation("预借款申请单查询")
    @PostMapping(value = "/queryAdvanceOrderList")
    ApiResponse<List<AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO>> queryAdvanceOrderList(@Validated @RequestBody AdvanceOrderListReq req);
}
