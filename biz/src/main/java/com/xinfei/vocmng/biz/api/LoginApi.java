/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.config.CustomEventPublisher;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.LoginSmsReq;
import com.xinfei.vocmng.biz.model.req.SendSmsReq;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.resp.DingTalkRedirectResp;
import com.xinfei.vocmng.biz.model.resp.LoginResp;
import com.xinfei.vocmng.biz.service.LoginService;
import com.xinfei.vocmng.biz.service.RepaymentTaskService;
import com.xinfei.vocmng.biz.service.SseService;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.zip.ZipOutputStream;
import javax.annotation.Resource;
import java.util.zip.ZipEntry;
/**
 *
 * <AUTHOR>
 * @version $ LoginApi, v 0.1 2023/12/19 15:39 wancheng.qu Exp $
 */

@Api(tags = "登陆相关")
@RestController
@Slf4j
@RequestMapping("/login")
public class LoginApi {

    @Resource
    private LoginService loginService;

    @Autowired
    private CustomEventPublisher eventPublisher;

    @Autowired
    private SseService sseService;

    @Autowired
    private RepaymentTaskService repaymentTaskService;

    @ApiOperation("发送短信")
    @PostMapping("/send")
    public ApiResponse<Boolean> sendSms(@Validated @RequestBody SendSmsReq req){
        return ApiResponse.success(loginService.send(req));
    }

    @ApiOperation("通过短信验证码登录")
    @PostMapping("/loginBySms")
    public ApiResponse<LoginResp> loginBySms(@Validated @RequestBody LoginSmsReq loginSmsReq){
        return ApiResponse.success(loginService.loginBySms(loginSmsReq));
    }

    @ApiOperation("获取钉钉扫码的二维码链接地址")
    @GetMapping("/dingtalkUrl")
    public ApiResponse<DingTalkRedirectResp> authorizeUrl(){
        return ApiResponse.success(loginService.authorizeUrl());
    }

    @ApiOperation("通过钉钉扫码登录")
    @GetMapping("/dingtalklogin")
    public ApiResponse<LoginResp> loginByDingTalk(@RequestParam("authCode") String authCode, @RequestParam("state") String state){
        return ApiResponse.success(loginService.loginByDingTalk(authCode,state));
    }

    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public ApiResponse<Boolean> logout(){
        return ApiResponse.success(loginService.logout());
    }

    @GetMapping("/updateCache")
    public ApiResponse<Boolean> updateCache(@RequestParam("rkey") String rkey){
        return ApiResponse.success(loginService.delRedisKey(rkey));
    }

    @GetMapping("/pubEvent")
    public ApiResponse<Boolean> pubEvent(@RequestParam("userIdentify") String userIdentify,@RequestParam("customerPhone") String customerPhone,@RequestParam("displayNumber") String displayNumber,@RequestParam("summaryId") Long summaryId){
        eventPublisher.publishEvent(userIdentify,customerPhone,displayNumber,summaryId);
        return ApiResponse.success(Boolean.TRUE);
    }

    @GetMapping(value = "/events", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter events(@RequestParam String userIdentify) {
        log.info("SSE connection establishment,userIdentify={}", userIdentify);
        if (StringUtils.isBlank(userIdentify)) {
            throw new com.xinfei.vocmng.itl.client.exeception.IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "userIdentify is null");
        }
        SseEmitter emitter = new SseEmitter(3600000L * 12);
        try {
            sseService.addEmitter(userIdentify, emitter);
        } catch (Exception e) {
            log.warn("SSE addEmitter error,userIdentify={},err={}", userIdentify, e.getMessage());
        }
        emitter.onCompletion(() -> {
            log.info("receive event end,userIdentify={}", userIdentify);
        });
        emitter.onError(throwable -> {
            log.warn("SSE onError , userIdentify={},err={}", userIdentify, throwable.getMessage());
            emitter.completeWithError(throwable);
        });
        emitter.onTimeout(() -> {
            log.warn("SSE connection timed out, userIdentify={}", userIdentify);
            emitter.complete();
        });
        return emitter;
    }

    @GetMapping("/runRepaymentTask")
    public ApiResponse<Boolean> runRepaymentTask(){
        repaymentTaskService.updateRepaymentStatus();
        return ApiResponse.success(Boolean.TRUE);
    }

    @GetMapping("/downloadFile")
    public void downloadFile(@RequestParam String url, HttpServletResponse response) throws Exception {
        URL fileUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) fileUrl.openConnection();
        connection.setRequestMethod("GET");
        connection.connect();
        InputStream inputStream = connection.getInputStream();

        String path = fileUrl.getPath();
        String fileName = path.substring(path.lastIndexOf('/') + 1);
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        response.setContentType("application/octet-stream");

        // 将文件流写入响应
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            response.getOutputStream().write(buffer, 0, length);
        }

        response.getOutputStream().flush();
        inputStream.close();
    }

    @PostMapping("/downloadZip")
    public void downloadZip(@RequestBody List<String> urls, HttpServletResponse response) throws Exception {
        response.setHeader("Content-Disposition", "attachment; filename=\"files.zip\"");
        response.setContentType("application/zip");

        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            for (String fileUrl : urls) {
                URL url = new URL(fileUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.connect();

                try (InputStream inputStream = connection.getInputStream()) {
                    // 从 URL 中提取文件名
                    String path = url.getPath();
                    String fileName = path.substring(path.lastIndexOf('/') + 1);
                    // 创建 ZIP 文件条目
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOutputStream.putNextEntry(zipEntry);

                    // 写入文件到 ZIP
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = inputStream.read(buffer)) != -1) {
                        zipOutputStream.write(buffer, 0, length);
                    }
                    zipOutputStream.closeEntry();
                }
            }
        }
    }

    @GetMapping("/syncUserData")
    public ApiResponse<String> syncUserData(){
        return ApiResponse.success(loginService.syncUserData());
    }


}