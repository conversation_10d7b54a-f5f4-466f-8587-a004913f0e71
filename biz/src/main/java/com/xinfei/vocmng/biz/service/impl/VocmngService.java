/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.model.req.UserNosRequest;
import com.xinfei.vocmng.biz.rr.request.UdeskSendMessageRequest;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryMapper;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.SmsFeignService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.util.enums.ErrDtlEnum;
import com.xinfei.vocmng.util.exception.VocmngException;
import com.xyf.cis.query.facade.dto.standard.response.MobileDTO;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.bind.ValidationException;
import java.time.LocalDate;
import java.util.Map;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ VocmngService, v 0.1 2025-04-17 17:17 junjie.yan Exp $
 */
@Service
@Slf4j
public class VocmngService {

    @Autowired
    private CommunicateSummaryMapper communicateSummaryMapper;

    @Resource
    private UserLabelMappingMapper userLabelMappingMapper;

    @Resource
    protected CisFacadeClient cisFacadeClient;

    @Resource
    protected CisFacadeClientService cisFacadeClientService;

    @Resource
    private CommonService commonService;

    @Resource
    private UserLabelService userLabelService;

    @Resource
    private SmsFeignService smsFeignService;


    public Integer getSummaryLogs(Long userNo) {
        LocalDate endTime = LocalDate.now();
        LocalDate startTime = endTime.plusDays(-7);
        return communicateSummaryMapper.getSummariesSevenDay(userNo, startTime);
    }

    /**
     * @param userNo 用户账号
     * @return 用户标签响应
     */
    public String getUserLabel(Long userNo) throws ValidationException {
        if (Objects.isNull(userNo)) {
            throw new ValidationException("userNo is null");
        }
        try {
            List<LabelDto> labelDtos = userLabelService.getUserLabel(userNo.toString());
            return labelDtos.stream()
                    .map(LabelDto::getName)
                    .collect(Collectors.joining(","));
        } catch (Exception e) {
            log.error("获取用户标签异常", e);
            return StringUtils.EMPTY;
        }
    }

    public Boolean getUserStatus(String mobile) {
        PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
        if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
            log.warn("No related CIS users found for mobile: {}", mobile);
            return false;
        }
        List<UserSearchDTO> userSearchDTOS = relatedCisUsersResult.getList().stream()
                .filter(user -> StringUtils.equalsAny(user.getApp(), VocConstants.APP_XYF, VocConstants.APP_XYF01))
                .filter(user -> user.getStatus() != null && user.getStatus() == 10)
                .collect(Collectors.toList());
        log.info("userSearchDTOS:{}", userSearchDTOS);
        return CollectionUtils.isNotEmpty(userSearchDTOS);
    }

    /**
     * 验证手机号和身份证后6位是否匹配
     *
     * @param mobile      手机号
     * @param idCardLast6 身份证后6位
     * @return 是否匹配
     */
    public Boolean verifyIdCardLast6(String mobile, String idCardLast6) {
        log.info("Verifying mobile: {} with ID card last 6 digits", mobile);
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(idCardLast6)) {
            log.warn("Mobile or ID card last 6 digits is empty");
            return false;
        }

        // 查询与该手机号关联的所有用户
        PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
        if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
            log.warn("No related CIS users found for mobile: {}", mobile);
            return false;
        }

        // 过滤出xyf和xyf01的用户
        List<UserSearchDTO> xyfUsers = relatedCisUsersResult.getList().stream()
                .filter(user -> StringUtils.equalsAny(user.getApp(), VocConstants.APP_XYF, VocConstants.APP_XYF01))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(xyfUsers)) {
            log.warn("No XYF users found for mobile: {}", mobile);
            return false;
        }

        // 遍历所有用户，查询其身份证信息并验证后6位
        for (UserSearchDTO user : xyfUsers) {
            try {
                ThreeElementsDTO threeElements = cisFacadeClient.queryThreeElementsByUserNo(user.getUserNo());
                if (threeElements != null && StringUtils.isNotEmpty(threeElements.getIdNo())) {
                    String idNo = threeElements.getIdNo();
                    // 获取身份证后6位
                    String last6 = idNo.substring(Math.max(0, idNo.length() - 6));
                    if (idCardLast6.equals(last6)) {
                        log.info("ID card last 6 digits match for user: {}", user.getUserNo());
                        return true;
                    }
                }
            } catch (Exception e) {
                log.error("Error querying three elements for user: {}", user.getUserNo(), e);
            }
        }

        log.info("No matching ID card found for mobile: {}", mobile);
        return false;
    }

    public void sendSms(UdeskSendMessageRequest req) {
        // 验证data中的value不能为空
        if (req.getData() != null) {
            for (Map.Entry<String, Object> entry : req.getData().entrySet()) {
                if (entry.getValue() == null ||
                    (entry.getValue() instanceof String && StringUtils.isBlank((String) entry.getValue()))) {
                    throw new VocmngException(ErrDtlEnum.PARAM_INVALID);
                }
            }
        }
        smsFeignService.smsSend(req.getMobile(), req.getTemplateId(), req.getApp(), req.getData());
    }
}