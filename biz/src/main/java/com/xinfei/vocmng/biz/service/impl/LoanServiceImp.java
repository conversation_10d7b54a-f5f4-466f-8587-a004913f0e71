/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.alibaba.excel.EasyExcel;
import com.google.gson.Gson;
import com.xinfei.fundcore.facade.api.response.FundOrderQueryResponse;
import com.xinfei.huttalegal.facade.rr.req.LawsuitAgencyQueryReq;
import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.lendtrade.facade.enums.OrderStatusEnum;
import com.xinfei.lendtrade.facade.rr.ManageOrderDetailRequest;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.lendtrade.facade.rr.dto.Page;
import com.xinfei.repaytrade.facade.RepaymentPlanFacade;
import com.xinfei.repaytrade.facade.rr.dto.FeeAmountDto;
import com.xinfei.repaytrade.facade.rr.enums.CalcSettleTypeEnum;
import com.xinfei.repaytrade.facade.rr.response.MultiRepayLoanCalcResponse;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentsByLoanNoResponse;
import com.xinfei.repaytrade.facade.rr.response.RepayLoanCalcResponse;
import com.xinfei.vocmng.biz.component.DictDataCache;
import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.mapstruct.DiversionOrderConverter;
import com.xinfei.vocmng.biz.mapstruct.OrderRequestConverter;
import com.xinfei.vocmng.biz.model.enums.DeductRuleEnum;
import com.xinfei.vocmng.biz.model.enums.PlanSourceEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.OrderListExcelReq;
import com.xinfei.vocmng.biz.rr.dto.DeductionInfoDto;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLatestBillDto;
import com.xinfei.vocmng.biz.rr.dto.IVRLoanSettlementDto;
import com.xinfei.vocmng.biz.rr.dto.IVRMonthlyAmountDto;
import com.xinfei.vocmng.biz.rr.dto.IVROrderInfoDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.OrderFilterFactorUnencryptedDto;
import com.xinfei.vocmng.biz.rr.dto.RepayFeeDetailDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDto;
import com.xinfei.vocmng.biz.rr.request.AgencyDetailRequest;
import com.xinfei.vocmng.biz.rr.request.ApiOrderRequest;
import com.xinfei.vocmng.biz.rr.request.CanRepayRequest;
import com.xinfei.vocmng.biz.rr.request.GetBillListRequest;
import com.xinfei.vocmng.biz.rr.request.GetBillListPageRequest;
import com.xinfei.vocmng.biz.rr.request.GetOrderDetailRequest;
import com.xinfei.vocmng.biz.rr.request.GetOrderListRequest;
import com.xinfei.vocmng.biz.rr.request.GetRepaymentsRequest;
import com.xinfei.vocmng.biz.rr.response.CanRepayResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.service.FundCoreService;
import com.xinfei.vocmng.biz.service.LoanService;
import com.xinfei.vocmng.biz.util.DataMaskingUtil;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.RedisServiceUtils;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanDetailMapper;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanMapper;
import com.xinfei.vocmng.dal.po.DictDetail;
import com.xinfei.vocmng.dal.po.RepaymentPlan;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.itl.ApiDiversionPhpFeignClient;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.feign.CisExtFacadeClient;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.HuTtaLegalClient;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.FundFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.ProductFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.RepayFacadeClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.SmsFeignService;
import com.xinfei.vocmng.itl.client.feign.service.RepayFacadeService;
import com.xinfei.vocmng.itl.client.feign.service.dto.RepayCalculateDto;
import com.xinfei.vocmng.itl.model.enums.SettleBaffleScene;
import com.xinfei.vocmng.itl.rr.ApiDataDiversionPhpReq;
import com.xinfei.vocmng.itl.rr.ApiDiversionPhpReq;
import com.xinfei.vocmng.itl.rr.ApiDiversionPhpResp;
import com.xinfei.vocmng.itl.rr.ApiResponse;
import com.xinfei.vocmng.itl.rr.DiversionOrderInfo;
import com.xinfei.vocmng.itl.rr.FinServiceType;
import com.xinfei.vocmng.itl.rr.Order;
import com.xinfei.vocmng.itl.rr.ProductBaseInfo;
import com.xinfei.vocmng.itl.rr.ProductDetailInfo;
import com.xinfei.vocmng.itl.rr.ProductResponse;
import com.xinfei.vocmng.itl.rr.ProfitProductResponse;
import com.xinfei.vocmng.itl.rr.QueryLoanDetailResp;
import com.xinfei.vocmng.itl.rr.QueryLoanReq;
import com.xinfei.vocmng.itl.rr.dto.BankDto;
import com.xinfei.vocmng.itl.rr.dto.FundOrderDto;
import com.xinfei.vocmng.util.threadpool.ContextInheritableThreadPoolExecutor;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.cis.query.facade.dto.standard.response.CustNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import io.kyoto.pillar.lcs.loan.domain.response.PlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version $ OrderBillServiceImp, v 0.1 2023-12-14 16:43 junjie.yan Exp $
 */
@Service
@Slf4j
public class LoanServiceImp implements LoanService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;

    private static final Executor smsExecutor = new ContextInheritableThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactory() {
                private final ThreadGroup threadGroup = new ThreadGroup("SmsThreadPool");
                private final AtomicInteger threadNumber = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(threadGroup, r, "Sms-thread-pool-" + threadNumber.getAndIncrement());
                }},
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;

    @Resource
    private LcsFeignService lcsFeignService;

    @Resource
    private FundFeignService fundFeignService;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private CommonService commonService;

    @Resource
    private ProductFeignService productFeignService;

    @Resource
    private RedisServiceUtils redisServiceUtils;

    @Resource
    private CisExtFacadeClient cisExtFacadeClient;

    @Resource
    private FundCoreService fundCoreService;

    @Resource
    private ApiDiversionPhpFeignClient apiDiversionPhpFeignClient;

    @Resource
    private RepaymentPlanMapper repaymentPlanMapper;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private HuTtaLegalClient huTtaLegalClient;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private DictDataCache dictDataCache;

    @Autowired
    private LcsFeignService lcsFeignClient;

    @Resource
    private RepayFacadeService repayFacadeService;

    @Autowired
    private SmsFeignService smsFeignService;

    /**
     * 资方借据单号互查新老接口开关
     */
    @Value("${xf.fund.flag:false}")
    private boolean fundFlag;

    /**
     * 是否可撤销开关
     */
    @Value("${isCanCancel:false}")
    private boolean isCanCancel;


    @Override
    public IVROrderInfoDto queryIVROrderList(String customNo, String mobile) {
        log.info("queryIVROrderList called with customNo: {}", customNo);
        IVROrderInfoDto ivrOrderInfoDto = new IVROrderInfoDto();

        if (StringUtils.isBlank(customNo) || StringUtils.isBlank(mobile)) {
            log.warn("queryIVROrderList received empty userNos list");
            return ivrOrderInfoDto;
        }

        ManageOrderListRequest manageOrderListRequest = new ManageOrderListRequest();
        manageOrderListRequest.setCustNos(Collections.singletonList(customNo));
        manageOrderListRequest.setOrderStatuses(Collections.singletonList(OrderStatusEnum.SUCCESS.getCode())); // 交易成功状态

        List<ManageOrderDetailDTO> needOrders = new ArrayList<>();
        Paging<OrderDto> orderDtoPaging = new Paging<>();
        GetOrderListRequest request = new GetOrderListRequest();
        request.setPageSize(50);
        request.setCurrentPage(1);

        // 批量获取所有符合条件订单
        getAllNeedOrders(orderDtoPaging, manageOrderListRequest, needOrders, request);
        if (CollectionUtils.isEmpty(needOrders)) {
            log.info("No orders found for customNo: {}", customNo);
            return ivrOrderInfoDto;
        }

        // 过滤出xyf和xyf01的数据用作后续处理
        List<ManageOrderDetailDTO> filteredOrders = needOrders.stream()
                .filter(order -> VocConstants.APP_XYF.equals(order.getInnerApp()) ||
                                VocConstants.APP_XYF01.equals(order.getInnerApp()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredOrders)) {
            log.info("No xyf or xyf01 orders found for customNo: {}", customNo);
            return ivrOrderInfoDto;
        }

        log.info("Filtered {} orders from {} total orders for customNo: {}",
                filteredOrders.size(), needOrders.size(), customNo);

        // 更新needOrders为过滤后的结果
        needOrders.clear();
        needOrders.addAll(filteredOrders);

        // 按创建时间排序，获取最近的订单
        needOrders.sort(Comparator.comparing(ManageOrderDetailDTO::getDateCreated).reversed());

        // 获取所有未结清订单的借据号
        List<String> loanNos = needOrders.stream()
                .map(ManageOrderDetailDTO::getLoanNo)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(loanNos)) {
            log.info("No loan numbers found for customNo: {}", customNo);
            return ivrOrderInfoDto;
        }

        // 只查询一次借据信息
        LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
        loanPlanRequest.setLoanNos(loanNos);
        List<LoanPlanResponse> loanPlanResponses;
        try {
            loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
        } catch (Exception e) {
            log.error("Failed to get loan plan details: {}", e.getMessage());
            return ivrOrderInfoDto;
        }

        if (CollectionUtils.isEmpty(loanPlanResponses)) {
            log.info("No loan plan responses found");
            return ivrOrderInfoDto;
        }

        // 获取当前月份
        LocalDate now = LocalDate.now();
        int currentMonth = now.getMonthValue();
        int currentYear = now.getYear();

        BigDecimal latestOrderAmount = BigDecimal.ZERO;
        BigDecimal totalMonthlyAmount;
        BigDecimal totalOverdueAmount = BigDecimal.ZERO;

        // 从所有订单的借据中找到最近一期未还的账单（按账单到期日排序）
        PlanResponse earliestUnrepaidPlan = null;
        Date earliestDueDate = null;

        for (LoanPlanResponse loanPlan : loanPlanResponses) {
            if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                // 遍历该借据下所有未还的账单，找到最早到期的
                for (PlanResponse plan : loanPlan.getPlanList()) {
                    if ("0".equals(plan.getRpyFlag()) && Objects.nonNull(plan.getDateDue())) {
                        Date currentDueDate = plan.getDateDue();

                        // 找到所有借据中最早到期的未还账单
                        if (earliestDueDate == null || currentDueDate.before(earliestDueDate)) {
                            log.info("Found earlier unrepaid plan: loanNo={}, term={}, dueDate={}",
                                    loanPlan.getLoanNo(), plan.getTerm(), currentDueDate);
                            earliestDueDate = currentDueDate;
                            earliestUnrepaidPlan = plan;
                        }
                    }
                }
            }
        }

        // 设置最近一期未还账单的账单日和应还金额
        if (Objects.nonNull(earliestUnrepaidPlan)) {
            // 设置最近一期未还账单的账单日
            if (Objects.nonNull(earliestUnrepaidPlan.getDateDue())) {
                ivrOrderInfoDto.setLatestBillDate(LocalDateTimeUtils.parseLocalDateByDate(earliestUnrepaidPlan.getDateDue()));
            }

            // 设置最近一期未还账单的应还金额
            if (Objects.nonNull(earliestUnrepaidPlan.getPlanFeeDetail()) &&
                Objects.nonNull(earliestUnrepaidPlan.getPlanFeeDetail().getActShouldRepay())) {
                latestOrderAmount = earliestUnrepaidPlan.getPlanFeeDetail().getActShouldRepay().getSumAmt();
            }
        }

        // 计算当月总应还金额和逾期金额
        BigDecimal currentMonthAmount = BigDecimal.ZERO; // 当月账单金额（不包含逾期）

        for (LoanPlanResponse loanPlan : loanPlanResponses) {
            if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                for (PlanResponse planResponse : loanPlan.getPlanList()) {
                    // 只处理未结清的账单
                    if (Objects.nonNull(planResponse.getDateDue())) {
                        // Java Date类型需要特殊处理
                        Date dateDue = planResponse.getDateDue();
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(dateDue);
                        int planMonth = cal.get(Calendar.MONTH) + 1; // 月份从0开始
                        int planYear = cal.get(Calendar.YEAR);

                        // 获取账单金额
                        BigDecimal planAmount = BigDecimal.ZERO;
                        if (planResponse.getPlanFeeDetail() != null &&
                            planResponse.getPlanFeeDetail().getActShouldRepay() != null) {
                            planAmount = planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt();
                        }

                        // 检查是否是当月账单
                        if (planMonth == currentMonth && planYear == currentYear && !"1".equals(planResponse.getRpyFlag())) {
                            currentMonthAmount = currentMonthAmount.add(planAmount);
                        }

                        // 检查是否是逾期账单，直接使用rpyFlag字段判断
                        if ("1".equals(planResponse.getRpyFlag())) {
                            log.info("Found overdue plan by rpyFlag: loanNo={}, term={}, dueDate={}, amount={}",
                                    loanPlan.getLoanNo(), planResponse.getTerm(), dateDue, planAmount);
                            totalOverdueAmount = totalOverdueAmount.add(planAmount);
                        }
                    }
                }
            }
        }

        // 计算当月总应还金额（当月账单金额 + 逾期金额）
        totalMonthlyAmount = currentMonthAmount.add(totalOverdueAmount);

        ivrOrderInfoDto.setLatestOrderAmount(latestOrderAmount);
        ivrOrderInfoDto.setTotalMonthlyAmount(totalMonthlyAmount);
        ivrOrderInfoDto.setTotalOverdueAmount(totalOverdueAmount);

        log.info("queryIVROrderList returning IVR info: {}", ivrOrderInfoDto);
        // 异步发送短信
//        smsExecutor.execute(() -> {
//            try {
//                Map<String, Object> data = new HashMap<>();
//                data.put("#kf_latestBillDate", Objects.nonNull(ivrOrderInfoDto.getLatestBillDate()) ? ivrOrderInfoDto.getLatestBillDate().format(DATE_FORMATTER) : "无");
//                data.put("#kf_latestOrderAmount", Objects.nonNull(ivrOrderInfoDto.getLatestOrderAmount()) ? ivrOrderInfoDto.getLatestOrderAmount().toString() : StringUtils.EMPTY);
//                data.put("#kf_totalMonthlyAmount", Objects.nonNull(ivrOrderInfoDto.getTotalMonthlyAmount()) ? ivrOrderInfoDto.getTotalMonthlyAmount().toString() :StringUtils.EMPTY);
//                data.put("#kf_totalOverdueAmount", Objects.nonNull(ivrOrderInfoDto.getTotalOverdueAmount()) ? ivrOrderInfoDto.getTotalOverdueAmount().toString() : StringUtils.EMPTY);
//                smsFeignService.smsSend(mobile, ApolloConstant.smsIvrLoanTemplateId, "xyf01", data);
//            } catch (Exception e) {
//                log.warn("Exception during SMS send", e);
//            }
//        });
        return ivrOrderInfoDto;
    }

    @Override
    public IVRLatestBillDto queryIVRLatestBill(String customNo, String mobile) {
        log.info("queryIVRLatestBill called with customNo: {}", customNo);
        IVRLatestBillDto ivrLatestBillDto = new IVRLatestBillDto();

        if (StringUtils.isBlank(customNo) || StringUtils.isBlank(mobile)) {
            log.warn("queryIVRLatestBill received empty parameters");
            return ivrLatestBillDto;
        }

        // 获取订单和借据信息的公共逻辑
        List<LoanPlanResponse> loanPlanResponses = getLoanPlanResponses(customNo);
        if (CollectionUtils.isEmpty(loanPlanResponses)) {
            log.info("No loan plan responses found for customNo: {}", customNo);
            return ivrLatestBillDto;
        }

        // 先查询减免方案信息，如果有减免方案就直接返回
        if (queryAndSetReductionPlanInfo(loanPlanResponses, ivrLatestBillDto, true)) {
            log.info("queryIVRLatestBill returning with reduction plan: {}", ivrLatestBillDto);
            return ivrLatestBillDto;
        }

        // 没有减免方案，继续查询原始账单信息
        // 从所有订单的借据中找到最近一期未还的账单（按账单到期日排序）
        PlanResponse earliestUnrepaidPlan = null;
        Date earliestDueDate = null;

        for (LoanPlanResponse loanPlan : loanPlanResponses) {
            if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                // 遍历该借据下所有未还的账单，找到最早到期的
                for (PlanResponse plan : loanPlan.getPlanList()) {
                    if ("0".equals(plan.getRpyFlag()) && Objects.nonNull(plan.getDateDue())) {
                        Date currentDueDate = plan.getDateDue();

                        // 找到所有借据中最早到期的未还账单
                        if (earliestDueDate == null || currentDueDate.before(earliestDueDate)) {
                            log.info("Found earlier unrepaid plan: loanNo={}, term={}, dueDate={}",
                                    loanPlan.getLoanNo(), plan.getTerm(), currentDueDate);
                            earliestDueDate = currentDueDate;
                            earliestUnrepaidPlan = plan;
                        }
                    }
                }
            }
        }

        // 设置最近一期未还账单的账单日和应还金额
        if (Objects.nonNull(earliestUnrepaidPlan)) {
            // 设置最近一期未还账单的账单日
            if (Objects.nonNull(earliestUnrepaidPlan.getDateDue())) {
                ivrLatestBillDto.setLatestBillDate(LocalDateTimeUtils.parseLocalDateByDate(earliestUnrepaidPlan.getDateDue()));
            }

            // 设置最近一期未还账单的应还金额
            if (Objects.nonNull(earliestUnrepaidPlan.getPlanFeeDetail()) &&
                Objects.nonNull(earliestUnrepaidPlan.getPlanFeeDetail().getActShouldRepay())) {
                BigDecimal latestOrderAmount = earliestUnrepaidPlan.getPlanFeeDetail().getActShouldRepay().getSumAmt();
                ivrLatestBillDto.setLatestOrderAmount(latestOrderAmount);
            }
        }

        // 计算所有逾期金额
        BigDecimal totalOverdueAmount = BigDecimal.ZERO;
        for (LoanPlanResponse loanPlan : loanPlanResponses) {
            if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                for (PlanResponse planResponse : loanPlan.getPlanList()) {
                    // 检查是否是逾期账单，直接使用rpyFlag字段判断
                    if ("1".equals(planResponse.getRpyFlag()) && Objects.nonNull(planResponse.getDateDue())) {
                        // 获取账单金额
                        BigDecimal planAmount = BigDecimal.ZERO;
                        if (planResponse.getPlanFeeDetail() != null &&
                            planResponse.getPlanFeeDetail().getActShouldRepay() != null) {
                            planAmount = planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt();
                        }

                        log.info("Found overdue plan by rpyFlag: loanNo={}, term={}, dueDate={}, amount={}",
                                loanPlan.getLoanNo(), planResponse.getTerm(), planResponse.getDateDue(), planAmount);
                        totalOverdueAmount = totalOverdueAmount.add(planAmount);
                    }
                }
            }
        }

        ivrLatestBillDto.setTotalOverdueAmount(totalOverdueAmount);

        log.info("queryIVRLatestBill returning: {}", ivrLatestBillDto);
        return ivrLatestBillDto;
    }

    @Override
    public IVRMonthlyAmountDto queryIVRMonthlyAmount(String customNo, String mobile) {
        log.info("queryIVRMonthlyAmount called with customNo: {}", customNo);
        IVRMonthlyAmountDto ivrMonthlyAmountDto = new IVRMonthlyAmountDto();

        if (StringUtils.isBlank(customNo) || StringUtils.isBlank(mobile)) {
            log.warn("queryIVRMonthlyAmount received empty parameters");
            return ivrMonthlyAmountDto;
        }

        // 获取订单和借据信息的公共逻辑
        List<LoanPlanResponse> loanPlanResponses = getLoanPlanResponses(customNo);
        if (CollectionUtils.isEmpty(loanPlanResponses)) {
            log.info("No loan plan responses found for customNo: {}", customNo);
            return ivrMonthlyAmountDto;
        }

        // 先查询减免方案信息，如果有减免方案就直接返回
        if (queryAndSetReductionPlanInfo(loanPlanResponses, ivrMonthlyAmountDto, false)) {
            log.info("queryIVRMonthlyAmount returning with reduction plan: {}", ivrMonthlyAmountDto);
            return ivrMonthlyAmountDto;
        }

        // 没有减免方案，继续查询原始账单信息
        // 获取当前月份
        LocalDate now = LocalDate.now();
        int currentMonth = now.getMonthValue();
        int currentYear = now.getYear();

        // 计算当月总应还金额和逾期金额
        BigDecimal currentMonthAmount = BigDecimal.ZERO; // 当月账单金额（不包含逾期）
        BigDecimal totalOverdueAmount = BigDecimal.ZERO;

        for (LoanPlanResponse loanPlan : loanPlanResponses) {
            if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                for (PlanResponse planResponse : loanPlan.getPlanList()) {
                    // 只处理未结清的账单
                    if (Objects.nonNull(planResponse.getDateDue())) {
                        // Java Date类型需要特殊处理
                        Date dateDue = planResponse.getDateDue();
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(dateDue);
                        int planMonth = cal.get(Calendar.MONTH) + 1; // 月份从0开始
                        int planYear = cal.get(Calendar.YEAR);

                        // 获取账单金额
                        BigDecimal planAmount = BigDecimal.ZERO;
                        if (planResponse.getPlanFeeDetail() != null &&
                            planResponse.getPlanFeeDetail().getActShouldRepay() != null) {
                            planAmount = planResponse.getPlanFeeDetail().getActShouldRepay().getSumAmt();
                        }

                        // 检查是否是当月账单
                        if (planMonth == currentMonth && planYear == currentYear && !"1".equals(planResponse.getRpyFlag())) {
                            currentMonthAmount = currentMonthAmount.add(planAmount);
                        }

                        // 检查是否是逾期账单，直接使用rpyFlag字段判断
                        if ("1".equals(planResponse.getRpyFlag())) {
                            log.info("Found overdue plan by rpyFlag: loanNo={}, term={}, dueDate={}, amount={}",
                                    loanPlan.getLoanNo(), planResponse.getTerm(), dateDue, planAmount);
                            totalOverdueAmount = totalOverdueAmount.add(planAmount);
                        }
                    }
                }
            }
        }

        // 计算当月总应还金额（当月账单金额 + 逾期金额）
        BigDecimal totalMonthlyAmount = currentMonthAmount.add(totalOverdueAmount);

        ivrMonthlyAmountDto.setTotalMonthlyAmount(totalMonthlyAmount);

        log.info("queryIVRMonthlyAmount returning: {}", ivrMonthlyAmountDto);
        return ivrMonthlyAmountDto;
    }

    @Override
    public IVRLoanSettlementDto queryIVRLoanSettlement(String customNo, String mobile) {
        log.info("queryIVRLoanSettlement called with customNo: {}", customNo);
        IVRLoanSettlementDto ivrLoanSettlementDto = new IVRLoanSettlementDto();

        if (StringUtils.isBlank(customNo) || StringUtils.isBlank(mobile)) {
            log.warn("queryIVRLoanSettlement received empty parameters");
            return ivrLoanSettlementDto;
        }

        // 获取订单和借据信息的公共逻辑
        List<LoanPlanResponse> loanPlanResponses = getLoanPlanResponses(customNo);
        if (CollectionUtils.isEmpty(loanPlanResponses)) {
            log.info("No loan plan responses found for customNo: {}", customNo);
            return ivrLoanSettlementDto;
        }

        // 收集所有未结清的借据号
        List<String> unsettledLoanNos = loanPlanResponses.stream()
                .filter(loanPlan -> CollectionUtils.isNotEmpty(loanPlan.getPlanList()))
                .filter(loanPlan -> loanPlan.getPlanList().stream()
                        .anyMatch(plan -> !"2".equals(plan.getRpyFlag())))
                .map(LoanPlanResponse::getLoanNo)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unsettledLoanNos)) {
            log.info("No unsettled loans found for customNo: {}", customNo);
            return ivrLoanSettlementDto;
        }

        // 设置在贷未结清订单数量
        ivrLoanSettlementDto.setUnsettledLoanCount(unsettledLoanNos.size());

        // 先查询减免方案信息，如果有减免方案就直接返回
        if (queryAndSetReductionPlanInfoByLoanNos(unsettledLoanNos, ivrLoanSettlementDto, "在贷未结清订单")) {
            log.info("queryIVRLoanSettlement returning with reduction plan: {}", ivrLoanSettlementDto);
            return ivrLoanSettlementDto;
        }

        // 没有减免方案，使用 multiRepayCalculate 试算结清所需金额
        try {
            MultiRepayLoanCalcResponse multiRepayResponse = repayFacadeClient.multiRepayCalculate(
                    unsettledLoanNos,
                    CalcSettleTypeEnum.SETTLE
            );

            if (Objects.nonNull(multiRepayResponse)) {
                ivrLoanSettlementDto.setTotalSettlementAmount(multiRepayResponse.getTransAmt());
                log.info("试算结清总金额: {}", multiRepayResponse.getTransAmt());
            } else {
                log.warn("multiRepayCalculate returned empty response for loanNos: {}", unsettledLoanNos);
                ivrLoanSettlementDto.setTotalSettlementAmount(BigDecimal.ZERO);
            }
        } catch (Exception e) {
            log.error("Error calling multiRepayCalculate for loanNos: {}, error: {}", unsettledLoanNos, e.getMessage());
            ivrLoanSettlementDto.setTotalSettlementAmount(BigDecimal.ZERO);
        }

        log.info("queryIVRLoanSettlement returning: {}", ivrLoanSettlementDto);
        return ivrLoanSettlementDto;
    }



    /**
     * 获取借据计划响应的公共方法
     */
    private List<LoanPlanResponse> getLoanPlanResponses(String customNo) {
        ManageOrderListRequest manageOrderListRequest = new ManageOrderListRequest();
        manageOrderListRequest.setCustNos(Collections.singletonList(customNo));
        manageOrderListRequest.setOrderStatuses(Collections.singletonList(OrderStatusEnum.SUCCESS.getCode())); // 交易成功状态

        List<ManageOrderDetailDTO> needOrders = new ArrayList<>();
        Paging<OrderDto> orderDtoPaging = new Paging<>();
        GetOrderListRequest request = new GetOrderListRequest();
        request.setPageSize(50);
        request.setCurrentPage(1);

        // 批量获取所有符合条件订单
        getAllNeedOrders(orderDtoPaging, manageOrderListRequest, needOrders, request);
        if (CollectionUtils.isEmpty(needOrders)) {
            log.info("No orders found for customNo: {}", customNo);
            return Collections.emptyList();
        }

        // 过滤出xyf和xyf01的数据用作后续处理
        List<ManageOrderDetailDTO> filteredOrders = needOrders.stream()
                .filter(order -> StringUtils.isNotBlank(order.getInnerApp()) &&
                        order.getInnerApp().startsWith(VocConstants.APP_XYF))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredOrders)) {
            log.info("No xyf or xyf01 orders found for customNo: {}", customNo);
            return Collections.emptyList();
        }

        log.info("Filtered {} orders from {} total orders for customNo: {}",
                filteredOrders.size(), needOrders.size(), customNo);

        // 按创建时间排序，获取最近的订单
        filteredOrders.sort(Comparator.comparing(ManageOrderDetailDTO::getDateCreated).reversed());

        // 获取所有未结清订单的借据号
        List<String> loanNos = filteredOrders.stream()
                .map(ManageOrderDetailDTO::getLoanNo)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(loanNos)) {
            log.info("No loan numbers found for customNo: {}", customNo);
            return Collections.emptyList();
        }

        // 只查询一次借据信息
        LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
        loanPlanRequest.setLoanNos(loanNos);
        List<LoanPlanResponse> loanPlanResponses;
        try {
            loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
        } catch (Exception e) {
            log.error("Failed to get loan plan details: {}", e.getMessage());
            return Collections.emptyList();
        }

        return loanPlanResponses;
    }

    /**
     * 查询并设置减免方案信息的公共方法（基于 LoanPlanResponse）
     *
     * @param loanPlanResponses 借据计划响应列表
     * @param dto 需要设置减免方案信息的DTO对象
     * @param isLatestBill 是否为近期账单查询（true：近期账单，false：当月应还）
     * @return 是否存在减免方案
     */
    private boolean queryAndSetReductionPlanInfo(List<LoanPlanResponse> loanPlanResponses, Object dto, boolean isLatestBill) {
        if (CollectionUtils.isEmpty(loanPlanResponses)) {
            setDefaultReductionPlanInfo(dto);
            return false;
        }

        // 根据查询类型筛选借据号
        List<String> targetLoanNos = filterLoanNosByQueryType(loanPlanResponses, isLatestBill);
        String queryType = isLatestBill ? "近期账单" : "当月应还";

        return queryAndSetReductionPlanInfoByLoanNos(targetLoanNos, dto, queryType);
    }

    /**
     * 查询并设置减免方案信息的通用方法（基于借据号列表）
     *
     * @param targetLoanNos 目标借据号列表
     * @param dto 需要设置减免方案信息的DTO对象
     * @param queryType 查询类型描述
     * @return 是否存在减免方案
     */
    private boolean queryAndSetReductionPlanInfoByLoanNos(List<String> targetLoanNos, Object dto, String queryType) {
        if (CollectionUtils.isEmpty(targetLoanNos)) {
            setDefaultReductionPlanInfo(dto);
            return false;
        }

        try {
            // 批量查询减免方案，isValid传2表示查询有效方案
            log.info("开始查询{}减免方案，借据号: {}", queryType, targetLoanNos);
            QueryRepaymentPlanResponse planResponse = repayFacadeClient.batchQueryRepaymentPlan(targetLoanNos, 2);

            if (planResponse != null && CollectionUtils.isNotEmpty(planResponse.getPlanDeductList())) {
                // 计算方案信息
                PlanCalculationResult calculationResult = calculatePlanTotalAmountAndCount(planResponse.getPlanDeductList());
                int planCount = calculationResult.getBillCount(); // 使用账单数量作为方案笔数
                BigDecimal planTotalAmount = calculationResult.getTotalAmount();
                LocalDate earliestDueDate = calculationResult.getEarliestDueDate();

                // 设置减免方案信息
                setReductionPlanInfo(dto, true, planCount, planTotalAmount, earliestDueDate);
                log.info("{}存在减免方案，借据号: {}, 方案笔数: {}, 总应还金额: {}, 最早到期时间: {}",
                        queryType, targetLoanNos, planCount, planTotalAmount, earliestDueDate);
                return true; // 存在减免方案
            } else {
                setDefaultReductionPlanInfo(dto);
                log.info("{}不存在减免方案，借据号: {}", queryType, targetLoanNos);
                return false; // 不存在减免方案
            }
        } catch (Exception e) {
            log.error("查询{}减免方案异常，借据号: {}, 错误: {}", queryType, targetLoanNos, e.getMessage(), e);
            setDefaultReductionPlanInfo(dto);
            return false; // 查询异常，视为无减免方案
        }
    }

    /**
     * 计算减免方案总金额和账单数量的通用方法
     */
    private PlanCalculationResult calculatePlanTotalAmountAndCount(List<QueryRepaymentPlanResponse.PlanDeduct> planDeductList) {
        BigDecimal planTotalAmount = BigDecimal.ZERO;
        Set<String> uniquePlanDetailIds = new HashSet<>(); // 用于去重planDetailId
        LocalDate earliestDueDate = null; // 最早方案到期时间

        log.info("查询到减免方案，方案数量: {}", planDeductList.size());
        for (QueryRepaymentPlanResponse.PlanDeduct planDeduct : planDeductList) {
            int currentPlanBillCount = planDeduct.getPlanDeductDetailList() != null ? planDeduct.getPlanDeductDetailList().size() : 0;

            log.info("方案借据号: {}, 方案明细数量: {}, 方案到期时间: {}",
                    planDeduct.getLoanNo(), currentPlanBillCount, planDeduct.getDateExpire());

            // 计算最早方案到期时间
            if (planDeduct.getDateExpire() != null) {
                Date planDueDate = planDeduct.getDateExpire();
                LocalDate planDueDateLocal = planDueDate.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                if (earliestDueDate == null || planDueDateLocal.isBefore(earliestDueDate)) {
                    earliestDueDate = planDueDateLocal;
                    log.info("更新最早方案到期时间: {}", earliestDueDate);
                }
            }

            // 收集所有planDetailId进行去重
            if (CollectionUtils.isNotEmpty(planDeduct.getPlanDeductDetailList())) {
                for (QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail detail : planDeduct.getPlanDeductDetailList()) {
                    if (StringUtils.isNotEmpty(detail.getPlanDetailId())) {
                        uniquePlanDetailIds.add(detail.getPlanDetailId());
                        log.debug("添加planDetailId: {}", detail.getPlanDetailId());
                    }
                }
            }

            RepayCalculateDto repayCalculateDto = repayFacadeService.repayCalculateDto(
                    StringUtils.equals("NO_SETTLE", planDeduct.getSettleType()) ? 1 : 2,
                    planDeduct.getTerms(),
                    planDeduct.getLoanNo(),
                    null,
                    null
            );

            if (repayCalculateDto != null && repayCalculateDto.getRealAmt() != null) {
                BigDecimal realAmt = repayCalculateDto.getRealAmt();
                log.info("方案借据号: {}, 期数: {}, 计算得到的应还金额: {}",
                        planDeduct.getLoanNo(), planDeduct.getTerms(), realAmt);
                planTotalAmount = planTotalAmount.add(realAmt);
            } else {
                log.warn("方案借据号: {}, 期数: {}, 无法获取应还金额",
                        planDeduct.getLoanNo(), planDeduct.getTerms());
            }
        }

        int totalBillCount = uniquePlanDetailIds.size(); // 使用去重后的planDetailId数量作为账单数量

        log.info("减免方案计算完成，总方案数: {}, 去重后账单数: {}, 总金额: {}, 最早到期时间: {}, 去重planDetailIds: {}",
                planDeductList.size(), totalBillCount, planTotalAmount, earliestDueDate, uniquePlanDetailIds);

        return new PlanCalculationResult(planTotalAmount, totalBillCount, earliestDueDate);
    }

    /**
     * 减免方案计算结果内部类
     */
    private static class PlanCalculationResult {
        private final BigDecimal totalAmount;
        private final int billCount;
        private final LocalDate earliestDueDate;

        public PlanCalculationResult(BigDecimal totalAmount, int billCount, LocalDate earliestDueDate) {
            this.totalAmount = totalAmount;
            this.billCount = billCount;
            this.earliestDueDate = earliestDueDate;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public int getBillCount() {
            return billCount;
        }

        public LocalDate getEarliestDueDate() {
            return earliestDueDate;
        }
    }

    /**
     * 根据查询类型筛选借据号
     */
    private List<String> filterLoanNosByQueryType(List<LoanPlanResponse> loanPlanResponses, boolean isLatestBill) {
        if (isLatestBill) {
            // 近期账单：查询最近一期未还账单对应的借据号，同时包含有逾期账单的借据号
            Date earliestDueDate = null;
            String latestBillLoanNo = null;
            Set<String> overdueLoanNos = new HashSet<>();

            for (LoanPlanResponse loanPlan : loanPlanResponses) {
                if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                    boolean hasOverdueBill = false;

                    for (PlanResponse plan : loanPlan.getPlanList()) {
                        // 查找最近一期未还账单
                        if ("0".equals(plan.getRpyFlag()) && Objects.nonNull(plan.getDateDue())) {
                            Date currentDueDate = plan.getDateDue();
                            if (earliestDueDate == null || currentDueDate.before(earliestDueDate)) {
                                earliestDueDate = currentDueDate;
                                latestBillLoanNo = loanPlan.getLoanNo();
                            }
                        }

                        // 查找逾期账单
                        if ("1".equals(plan.getRpyFlag())) {
                            hasOverdueBill = true;
                        }
                    }

                    // 如果该借据有逾期账单，加入逾期借据号集合
                    if (hasOverdueBill) {
                        overdueLoanNos.add(loanPlan.getLoanNo());
                    }
                }
            }

            // 合并最近账单借据号和逾期借据号
            Set<String> targetLoanNos = new HashSet<>();
            if (latestBillLoanNo != null) {
                targetLoanNos.add(latestBillLoanNo);
            }
            targetLoanNos.addAll(overdueLoanNos);

            log.info("近期账单查询减免方案，最近账单借据号: {}, 逾期借据号: {}, 合并后目标借据号: {}",
                    latestBillLoanNo, overdueLoanNos, targetLoanNos);
            return new ArrayList<>(targetLoanNos);
        } else {
            // 当月应还：只查询当月有账单的借据号（包括当月账单和逾期账单）
            LocalDate now = LocalDate.now();
            int currentMonth = now.getMonthValue();
            int currentYear = now.getYear();

            Set<String> currentMonthLoanNos = new HashSet<>();
            for (LoanPlanResponse loanPlan : loanPlanResponses) {
                if (CollectionUtils.isNotEmpty(loanPlan.getPlanList())) {
                    boolean hasCurrentMonthBill = false;
                    for (PlanResponse planResponse : loanPlan.getPlanList()) {
                        if (Objects.nonNull(planResponse.getDateDue())) {
                            Date dateDue = planResponse.getDateDue();
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(dateDue);
                            int planMonth = cal.get(Calendar.MONTH) + 1;
                            int planYear = cal.get(Calendar.YEAR);

                            // 检查是否是当月账单（未还）或逾期账单
                            if ((planMonth == currentMonth && planYear == currentYear && !"1".equals(planResponse.getRpyFlag())) ||
                                "1".equals(planResponse.getRpyFlag())) {
                                hasCurrentMonthBill = true;
                                break;
                            }
                        }
                    }

                    if (hasCurrentMonthBill) {
                        currentMonthLoanNos.add(loanPlan.getLoanNo());
                    }
                }
            }

            log.info("当月应还查询减免方案，目标借据号: {}", currentMonthLoanNos);
            return new ArrayList<>(currentMonthLoanNos);
        }
    }

    /**
     * 设置减免方案信息
     */
    private void setReductionPlanInfo(Object dto, Boolean hasReductionPlan, Integer planCount, BigDecimal planTotalAmount, LocalDate earliestDueDate) {
        if (dto instanceof IVRLatestBillDto) {
            IVRLatestBillDto latestBillDto = (IVRLatestBillDto) dto;
            latestBillDto.setHasReductionPlan(hasReductionPlan);
            latestBillDto.setPlanCount(planCount);
            latestBillDto.setPlanTotalAmount(planTotalAmount);
            latestBillDto.setEarliestPlanDueDate(earliestDueDate);
        } else if (dto instanceof IVRMonthlyAmountDto) {
            IVRMonthlyAmountDto monthlyAmountDto = (IVRMonthlyAmountDto) dto;
            monthlyAmountDto.setHasReductionPlan(hasReductionPlan);
            monthlyAmountDto.setPlanCount(planCount);
            monthlyAmountDto.setPlanTotalAmount(planTotalAmount);
            monthlyAmountDto.setEarliestPlanDueDate(earliestDueDate);
        } else if (dto instanceof IVRLoanSettlementDto) {
            IVRLoanSettlementDto loanSettlementDto = (IVRLoanSettlementDto) dto;
            loanSettlementDto.setHasReductionPlan(hasReductionPlan);
            loanSettlementDto.setPlanCount(planCount);
            loanSettlementDto.setPlanTotalAmount(planTotalAmount);
            loanSettlementDto.setEarliestPlanDueDate(earliestDueDate);
        }
    }

    /**
     * 设置默认减免方案信息（无方案）
     */
    private void setDefaultReductionPlanInfo(Object dto) {
        setReductionPlanInfo(dto, false, 0, BigDecimal.ZERO, null);
    }


    @Override
    public Paging<OrderDto> queryOrderList(GetOrderListRequest request) {
        ManageOrderListRequest manageOrderListRequest = OrderRequestConverter.INSTANCE.requestToManageOrderList(request);

        if (!StringUtils.isEmpty(request.getIdCard())) {
            CustNoDTO custNoDTO = cisFacadeClient.queryCustNoByIdNo(request.getIdCard());
            if (custNoDTO == null || StringUtils.isEmpty(custNoDTO.getCustNo())) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "此身份证号码无法查询到合法custNo");
            }
            manageOrderListRequest.setCustNos(Collections.singletonList(custNoDTO.getCustNo()));
        }

        if (StringUtils.isNotEmpty(request.getCustNo())) {
            manageOrderListRequest.setCustNos(Collections.singletonList(request.getCustNo()));
        }

        if (StringUtils.isNotEmpty(request.getApp())) {
            manageOrderListRequest.setApp(request.getApp());
        }

        if (!StringUtils.isEmpty(request.getMobile())) {
            PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(request.getMobile(), null, null, 1, 30);

            if (CollectionUtils.isEmpty(pageResult.getList())) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "此手机号码无法查询到合法userNo");
            }
            List<UserSearchDTO> custNoUsers = commonService.getUserByMobileAndCust(request.getMobile());
            Set<Long> userNos = new HashSet<>();
            custNoUsers.stream()
                    .map(UserSearchDTO::getUserNo)
                    .forEach(userNos::add);

            pageResult.getList().stream()
                    .map(UserSearchDTO::getUserNo)
                    .forEach(userNos::add);
            manageOrderListRequest.setUserNos(new ArrayList<>(userNos));
        }

        return getOrderDtoList(request, manageOrderListRequest);
    }

    @Override
    public OrderDto queryOrderDetail(GetOrderDetailRequest request) {
        ManageOrderDetailRequest manageOrderDetailRequest = new ManageOrderDetailRequest();
        manageOrderDetailRequest.setOrderNo(request.getOrderNo());
        ManageOrderDetailDTO manageOrderDetailDTO = lendQueryFacadeClient.getOrderDetail(manageOrderDetailRequest);
        if (manageOrderDetailDTO == null) {
            throw new IgnoreException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, "此订单号查不到订单orderNo" + request.getOrderNo());
        }
        OrderDto orderDto = OrderRequestConverter.INSTANCE.manageOrderRespToOrderDto(manageOrderDetailDTO);

        getThirdLoanNo(null, orderDto);

        if (StringUtils.isNotEmpty(manageOrderDetailDTO.getLoanNo())) {
            getOrderInfoFromLcs(orderDto, manageOrderDetailDTO.getLoanNo());
        }

        if (StringUtils.isNotEmpty(manageOrderDetailDTO.getFundLoanApplySerialNo())) {
            try {
                if (fundFlag) {
                    FundOrderQueryResponse response = fundFeignService.queryLoanNewQuery(null, manageOrderDetailDTO.getFundLoanApplySerialNo());
                    if (response != null) {
                        orderDto.setOutOrderNumber(response.getOutOrderNumber());
                    }
                } else {
                    orderDto.setOutOrderNumber(fundFeignService.queryLoanQuery(null, manageOrderDetailDTO.getFundLoanApplySerialNo()).getOutOrderNumber());
                }
            } catch (ClientException e) {
                log.error("queryOrderDetail获取资方订单号失败，订单号：{}，原因：{}", orderDto.getOrderNo(), e.getMessage());
            }
        }

        if ("MAIN".equals(request.getOrderType())) {
            //获取资方订单号是否可以取消额度
            orderDto.setIsLogoutQuota(fundCoreService.logoutQuotaCheck(request.getOrderNo()));
        }

        if (manageOrderDetailDTO.getBizData() != null && !manageOrderDetailDTO.getBizData().isEmpty()) {
            orderDto.setPreLoanInfo(manageOrderDetailDTO.getBizData().get("preLoanInfo"));
        }

        try {
            List<Order> orders = fundFeignService.getOrderList(null, orderDto.getOrderNo());
            if (!orders.isEmpty()) {
                orderDto.setChannelOrderNumber(orders.get(0).getOutOrderNumber());
            }
        } catch (ClientException e) {
            log.error("queryOrderDetail获取渠道订单号失败，订单号：{}，原因：{}", orderDto.getOrderNo(), e.getMessage());
        }

        try {
            List<OrderDto> orderDtos = new ArrayList<>();
            orderDtos.add(orderDto);
            getProductInfo(orderDtos);
        } catch (ClientException e) {
            log.warn("获取营收产品信息错误");
        }

        try {
            if (StringUtils.isNotEmpty(orderDto.getBankcardId()) && StringUtils.isNotEmpty(orderDto.getCustNo())) {
                BankCardResponse response = cisExtFacadeClient.queryBankCardInfoById(orderDto.getCustNo(), Long.parseLong(orderDto.getBankcardId()));
                if (response != null) {
                    orderDto.setBankName(response.getBankName());
                    orderDto.setBankcardNo(response.getCardNo());
                }
            }
        } catch (ClientException e) {
            log.warn("获取入账银行信息错误");
        }

        //根据借据号获取方案
        try {
            if (StringUtils.isNotEmpty(manageOrderDetailDTO.getLoanNo())) {
                getPlanSource(orderDto);
            }
        } catch (Exception e) {
            log.warn("获取方案来源失败，订单号：{}，原因：{}", orderDto.getOrderNo(), e.getMessage());
        }

        return orderDto;
    }

    @Override
    public Boolean orderCancel(String orderNo) {
        return lendQueryFacadeClient.orderCancel(orderNo);
    }

    private void getPlanSource(OrderDto orderDto) {
        if ("FP".equals(orderDto.getLoanStatus())) {
            return;
        }
        RepaymentPlan effectPlan = repaymentPlanDetailMapper.queryEffectPlan(orderDto.getLoanNo(), null);
        if (Objects.nonNull(effectPlan)) {
            RepaymentPlanDetail repaymentPlanDetail = repaymentPlanDetailMapper.queryPlanDetailByLoanNoAndPlanId(orderDto.getLoanNo(), effectPlan.getId());
            if (commonService.disabledPlan(Collections.singletonList(repaymentPlanDetail), effectPlan)) {
                orderDto.setPlanSource(PlanSourceEnum.HUTTA.getCode());
            } else {
                orderDto.setPlanSource(PlanSourceEnum.VOCMNG.getCode());
            }
        } else {
            if (commonService.hasEffectivePlan(orderDto.getLoanNo())) {
                orderDto.setPlanSource(PlanSourceEnum.HUTTA.getCode());
            }
        }
    }

    public void getOrderInfoFromLcs(OrderDto orderDto, String loanNo) {
        try {
            //借据状态
            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setLoanNos(Collections.singletonList(loanNo));
            List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);

            if (!loanPlanResponses.isEmpty() && loanPlanResponses.get(0) != null) {
                if (loanPlanResponses.get(0).getCompleteType() != null) {
                    switch (loanPlanResponses.get(0).getCompleteType()) {
                        case MISS_DEDUCT:
                            orderDto.setCompleteType("MISS_DEDUCT");
                            break;
                        case COMPLETE:
                            orderDto.setCompleteType("COMPLETE");
                            break;
                        default:
                            break;
                    }
                }
                orderDto.setLoanStatus(loanPlanResponses.get(0).getStatus());
                orderDto.setIntCalType(loanPlanResponses.get(0).getIntCalType());
                orderDto.setFeeRate(loanPlanResponses.get(0).getFeeRate());
                if (loanPlanResponses.get(0).getDateSettle() != null) {
                    orderDto.setDateSettle(LocalDateTime.ofInstant(loanPlanResponses.get(0).getDateSettle().toInstant(), ZoneId.of("Asia/Shanghai")));
                }
            }
        } catch (ClientException e) {
            log.error("获取 借据状态/最后结清时间/利率 失败，原因：{}", e.getMessage());
        }
    }

    private Paging<OrderDto> getOrderDtoList(GetOrderListRequest request, ManageOrderListRequest orderListRequest) {
        List<ManageOrderDetailDTO> needOrders = new ArrayList<>();
        Paging<OrderDto> orderDtoPaging = new Paging<>();

        //批量获取所有符合条件订单
        getAllNeedOrders(orderDtoPaging, orderListRequest, needOrders, request);
        if (CollectionUtils.isEmpty(needOrders)) {
            return orderDtoPaging;
        }

        //对于交易成功订单赋值借据信息
        List<OrderDto> orderDtos = loanInfo(needOrders);

        if (CollectionUtils.isNotEmpty(orderDtos) && StringUtils.isNotBlank(orderDtos.get(0).getCustNo())) {
            getInnerAppAndCapitalPool(orderDtos);
        }

        //根据借据状态进行筛选
        if (CollectionUtils.isNotEmpty(request.getLoanStatus())) {
            orderDtos = orderDtos.stream().filter(r -> request.getLoanStatus().contains(r.getLoanStatus())).collect(Collectors.toList());
        }

        //根据借据结清时间进行筛选
        if (request.getDateSettleStart() != null && request.getDateSettleEnd() != null) {
            orderDtos = orderDtos.stream().filter(r -> r.getDateSettle() != null && r.getDateSettle().isAfter(request.getDateSettleStart()) && r.getDateSettle().isBefore(request.getDateSettleEnd())).collect(Collectors.toList());
        }

        //根据innerApp条件进行筛选
        if (CollectionUtils.isNotEmpty(request.getInnerAppList())) {
            orderDtos = orderDtos.stream()
                    .filter(orderDto -> request.getInnerAppList().contains(orderDto.getInnerApp()))
                    .collect(Collectors.toList());
        }

        //根据资金池条件进行筛选
        if (CollectionUtils.isNotEmpty(request.getCapitalPoolList()) && StringUtils.isNotBlank(request.getCustNo())) {
            orderDtos = capitalFilter(request, orderDtos);
        }

        //查询出所有符合条件数据后，进行总数设置、内部分页
        orderDtoPaging.setTotal(orderDtos.size());
        orderDtos = orderDtos.stream()
                .skip((long) (request.getCurrentPage() - 1) * request.getPageSize())
                .limit(request.getPageSize()).
                collect(Collectors.toList());

        orderDtoPaging.setList(orderDtos);

        if ("PROFIT".equals(request.getOrderType())) {
            //营收订单的情况下 调用现金订单接口获取 loanAmt字段作为现金订单本金返回
            List<String> mainLoanReqNoList = orderDtos.stream().map(OrderDto::getMainLoanReqNo).collect(Collectors.toList());
            orderListRequest.setOrderNos(mainLoanReqNoList);
            orderListRequest.setOrderType("MAIN");
            List<ManageOrderDetailDTO> dtoList = lendQueryFacadeClient.getOrderList(orderListRequest).getPageList();
            for (OrderDto orderDto : orderDtos) {
                for (ManageOrderDetailDTO dto : dtoList) {
                    if (orderDto.getMainLoanReqNo().equals(dto.getLoanReqNo())) {
                        orderDto.setMainOrderPrinAmt(dto.getLoanAmt());
                        break;
                    }
                }
            }
        }

        //额外的借据信息设置
        extraLoanInfo(orderDtos, request);

        // 获取资方借据号
        if(CollectionUtils.isNotEmpty(orderDtos)){
            getThirdLoanNo(orderDtos, null);
        }

        return orderDtoPaging;
    }

    private void getThirdLoanNo(List<OrderDto> orderDtos, OrderDto orderDto) {
        QueryLoanReq queryLoanReq = new QueryLoanReq();
        List<String> loanNos;
        if (orderDto != null) {
            loanNos = Arrays.asList(orderDto.getLoanNo());
        } else {
            loanNos = orderDtos.stream()
                    .map(OrderDto::getLoanNo)
                    .collect(Collectors.toList());
        }
        queryLoanReq.setLoanNoList(loanNos);
        List<QueryLoanDetailResp> res = lcsFeignClient.queryLoanInfo(queryLoanReq);
        if (CollectionUtils.isNotEmpty(res)) {
            // 1. 创建 Map
            Map<String, String> loanNoToThirdLoanNoMap = res.stream()
                    .filter(resp -> resp.getLoanInfo() != null)
                    .collect(Collectors.toMap(
                            resp -> resp.getLoanInfo().getLoanNo(),
                            resp -> resp.getLoanInfo().getThirdLoanNo(),
                            (v1, v2) -> v1
                    ));

            if (orderDto != null) {
                orderDto.setThirdLoanNo(loanNoToThirdLoanNoMap.get(orderDto.getLoanNo()));
            } else {
                orderDtos.stream()
                        .filter(order -> loanNoToThirdLoanNoMap.containsKey(order.getLoanNo()))
                        .forEach(order -> order.setThirdLoanNo(loanNoToThirdLoanNoMap.get(order.getLoanNo())));
            }
        }
    }

    private List<OrderDto> capitalFilter(GetOrderListRequest request, List<OrderDto> orderDtos) {
        Gson gson = new Gson();

        // 从 Redis 获取数据并解析为 orderFilterFactorDto
        OrderFilterFactorUnencryptedDto orderFilterFactorDto = gson.fromJson(redisUtils.get(request.getCustNo()), OrderFilterFactorUnencryptedDto.class);
        Map<String, String> capitalPoolEncrypt = orderFilterFactorDto.getCapitalPoolEncrypt();
        List<String> capitalPoolList = request.getCapitalPoolList();

        if (capitalPoolEncrypt != null) {
            // 用于存储所有符合条件的 capitalPool
            Set<String> validCapitalPools = new HashSet<>();

            // 先构建所有有效的 capitalPool 值
            for (String key : capitalPoolList) {
                if (capitalPoolEncrypt.containsKey(key)) {
                    // 如果 key 在 capitalPoolEncrypt 中，添加加密后的值
                    validCapitalPools.add(capitalPoolEncrypt.get(key));
                } else {
                    // 否则，添加原始的 key 值
                    validCapitalPools.add(key);
                }
            }

            // 通过 validCapitalPools 集合一次性过滤 orderDtos
            orderDtos = orderDtos.stream()
                    .filter(order -> validCapitalPools.contains(order.getCapitalPool()))
                    .collect(Collectors.toList());
        }
        return orderDtos;
    }

    private void getInnerAppAndCapitalPool(List<OrderDto> orderDtos) {
        String custNo = orderDtos.get(0).getCustNo();
        // 获取 innerAppList 和 capitalPoolList
        List<String> innerAppList = getDistinctValues(orderDtos, OrderDto::getInnerApp);
        List<String> capitalPoolList = getDistinctValues(orderDtos, OrderDto::getCapitalPool);
        OrderFilterFactorUnencryptedDto orderFilterFactorDto = new OrderFilterFactorUnencryptedDto();
        //将资金池池信息加密
        Map<String, String> capitalPoolEncryptMap = new HashMap<>();
        List<DictDetail> allDictById = dictDataCache.getAllDictById(1L);
        for (int i = 0; i < capitalPoolList.size(); i++) {
            String capitalPoolEncrypt = DataMaskingUtil.maskMoney(capitalPoolList.get(i), allDictById);
            capitalPoolEncryptMap.put(capitalPoolEncrypt, capitalPoolList.get(i));
        }

        if (StringUtils.isNotBlank(redisUtils.get(custNo))) {
            Gson gson = new Gson();
            // 从 Redis 获取数据并解析为 orderFilterFactorDto
            orderFilterFactorDto = gson.fromJson(redisUtils.get(custNo), OrderFilterFactorUnencryptedDto.class);
            if (orderFilterFactorDto != null) {
                if (orderDtos.size() > orderFilterFactorDto.getOrderSize()) {
                    updateRedis(orderFilterFactorDto, orderDtos.size(), innerAppList, capitalPoolList, custNo, capitalPoolEncryptMap);
                }
            }
        } else {
            updateRedis(orderFilterFactorDto, orderDtos.size(), innerAppList, capitalPoolList, custNo, capitalPoolEncryptMap);
        }

    }

    private void getAllNeedOrders(Paging<OrderDto> orderDtoPaging, ManageOrderListRequest orderListRequest, List<ManageOrderDetailDTO> needOrders, GetOrderListRequest request) {
        orderListRequest.setPageSize(50);
        int n = 1;
        Page<ManageOrderDetailDTO> respDTO;
        do {
            orderListRequest.setPageNo(n);
            respDTO = lendQueryFacadeClient.getOrderList(orderListRequest);
            if (CollectionUtils.isEmpty(respDTO.getPageList())) {
                break;
            }
            needOrders.addAll(respDTO.getPageList());
            n++;
        } while (respDTO.getPageList().size() == 50);

        orderDtoPaging.setPageSize(request.getPageSize());
        orderDtoPaging.setCurrentPage(request.getCurrentPage());
    }

    private List<OrderDto> loanInfo(List<ManageOrderDetailDTO> needOrders) {
        //不为空的借据号
        List<String> loanNos = needOrders.stream().map(ManageOrderDetailDTO::getLoanNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        //不为空的借据号查出借据信息
        List<LoanPlanResponse> loanPlanResponses = getLoanPlanResponses(loanNos);

        List<OrderDto> orderDtos = new ArrayList<>();
        Map<String, LoanPlanResponse> loanPlanMap = loanPlanResponses.stream().collect(Collectors.toMap(LoanPlanResponse::getLoanNo, r -> r));
        for (ManageOrderDetailDTO manageOrderDetailDTO : needOrders) {
            OrderDto orderDto = OrderRequestConverter.INSTANCE.manageOrderRespToOrderDto(manageOrderDetailDTO);
            LoanPlanResponse loanPlan = loanPlanMap.get(manageOrderDetailDTO.getLoanNo());
            //获取预借款订单信息
            if (manageOrderDetailDTO.getBizData() != null && !manageOrderDetailDTO.getBizData().isEmpty()) {
                orderDto.setPreLoanInfo(manageOrderDetailDTO.getBizData().get("preLoanInfo"));
            }
            if (loanPlan == null || StringUtils.isEmpty(manageOrderDetailDTO.getLoanNo())) {
                orderDtos.add(orderDto);
                continue;
            }
            //赋值借据信息
            orderDto.setLoanStatus(loanPlan.getStatus());
            orderDto.setIntCalType(loanPlanResponses.get(0).getIntCalType());
            orderDto.setFeeRate(loanPlan.getFeeRate());
            if (loanPlan.getDateSettle() != null) {
                orderDto.setDateSettle(LocalDateTime.ofInstant(loanPlan.getDateSettle().toInstant(), ZoneId.of("Asia/Shanghai")));
            }
            if (loanPlan.getCompleteType() != null) {
                switch (loanPlan.getCompleteType()) {
                    case MISS_DEDUCT:
                        orderDto.setCompleteType("MISS_DEDUCT");
                        break;
                    case COMPLETE:
                        orderDto.setCompleteType("COMPLETE");
                        break;
                    default:
                        break;
                }
            }

            //最近账单日(取最早一期未还账单的账单日，若无则不返回)
            List<PlanResponse> planResponseList = loanPlan.getPlanList().stream().filter(r -> !"2".equals(r.getRpyFlag())).sorted(Comparator.comparing(PlanResponse::getTerm)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planResponseList)) {
                orderDto.setDateDue(LocalDateTimeUtils.parseLocalDateByDate(planResponseList.get(0).getDateDue()));
            }

            orderDtos.add(orderDto);
        }

        return orderDtos;
    }

    private void extraLoanInfo(List<OrderDto> orderDtos, GetOrderListRequest request) {
        // 批量查询减免方案并设置
        setFeeRateLimitFlag(orderDtos);

        for (OrderDto orderDto : orderDtos) {
            if (!request.getIsCustomerDetail()) {
                //资方订单号
                if (!StringUtils.isEmpty(request.getOutOrderNumber())) {
                    orderDto.setOutOrderNumber(request.getOutOrderNumber());
                } else if (StringUtils.isNotEmpty(orderDto.getFundLoanApplySerialNo())) {
                    try {
                        if (fundFlag) {
                            FundOrderQueryResponse response = fundFeignService.queryLoanNewQuery(null, orderDto.getFundLoanApplySerialNo());
                            if (response != null) {
                                orderDto.setOutOrderNumber(response.getOutOrderNumber());
                            }
                        } else {
                            orderDto.setOutOrderNumber(fundFeignService.queryLoanQuery(null, orderDto.getFundLoanApplySerialNo()).getOutOrderNumber());
                        }
                    } catch (ClientException e) {
                        log.warn("获取资方订单号失败，订单号：{}，原因：{}", orderDto.getOrderNo(), e.getMessage());
                    }
                }

                //渠道订单号
                if (!StringUtils.isEmpty(request.getChannelOrderNumber())) {
                    orderDto.setChannelOrderNumber(request.getChannelOrderNumber());
                } else {
                    try {
                        List<Order> orders = fundFeignService.getOrderList(null, orderDto.getOrderNo());
                        if (!orders.isEmpty()) {
                            orderDto.setChannelOrderNumber(orders.get(0).getOutOrderNumber());
                        }
                    } catch (ClientException e) {
                        log.warn("获取渠道订单号失败，订单号：{}，原因：{}", orderDto.getOrderNo(), e.getMessage());
                    }
                }
            }

            try {
                getProductInfo(orderDtos);
            } catch (ClientException e) {
                log.warn("获取营收产品信息错误");
            }

            //查询订单的生效中方案来源
            try {
                getPlanSource(orderDto);
            } catch (Exception e) {
                log.warn("获取方案来源失败，订单号：{}，原因：{}", orderDto.getOrderNo(), e.getMessage());
            }
        }
    }

    /**
     * 批量查询减免方案并设置feeRateLimitFlag字段
     *
     * @param orderDtos 订单列表
     */
    private void setFeeRateLimitFlag(List<OrderDto> orderDtos) {
        if (CollectionUtils.isEmpty(orderDtos)) {
            return;
        }

        try {
            // 批量取出loanNo
            List<String> loanNos = orderDtos.stream()
                    .map(OrderDto::getLoanNo)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(loanNos)) {
                return;
            }

            // 批量查询减免方案，isValid传2表示查询有效方案
            log.info("开始批量查询减免方案，借据号数量: {}", loanNos.size());
            QueryRepaymentPlanResponse planResponse = repayFacadeClient.batchQueryRepaymentPlan(loanNos, 2);

            if (planResponse != null && CollectionUtils.isNotEmpty(planResponse.getPlanDeductList())) {
                // 创建借据号到减免方案类型的映射
                Map<String, String> loanNoToFeeRateLimitFlagMap = new HashMap<>();

                for (QueryRepaymentPlanResponse.PlanDeduct planDeduct : planResponse.getPlanDeductList()) {
                    if (StringUtils.isNotEmpty(planDeduct.getLoanNo())) {
                        loanNoToFeeRateLimitFlagMap.put(planDeduct.getLoanNo(), planDeduct.getDeductRule());
                    }
                }

                // 设置orderDtos中的feeRateLimitFlag字段
                for (OrderDto orderDto : orderDtos) {
                    if (StringUtils.isNotEmpty(orderDto.getLoanNo()) &&
                        loanNoToFeeRateLimitFlagMap.containsKey(orderDto.getLoanNo())) {
                        orderDto.setDeductRule(loanNoToFeeRateLimitFlagMap.get(orderDto.getLoanNo()));
                    }
                }

                log.info("批量查询减免方案完成，找到锁定费率方案数量: {}", loanNoToFeeRateLimitFlagMap.size());
            } else {
                log.info("批量查询减免方案完成，未找到有效减免方案");
            }
        } catch (Exception e) {
            log.error("批量查询减免方案异常，借据号数量: {}, 错误: {}",
                    orderDtos.size(), e.getMessage(), e);
        }
    }

    @NotNull
    private List<LoanPlanResponse> getLoanPlanResponses(List<String> loanNos) {
        List<LoanPlanResponse> loanPlanResponses = new ArrayList<>();
        if (CollectionUtils.isEmpty(loanNos)) {
            return loanPlanResponses;
        }
        int priIndex;
        // 要截取的下标下限
        int lastIndex;
        // 每次插入list的数量
        int subNum = 20;
        // 查询出来list的总数目
        int totalNum = loanNos.size();
        // 总共需要插入的次数
        int insertTimes = totalNum / subNum;
        List<String> subLoanNos;
        for (int i = 0; i <= insertTimes; i++) {
            priIndex = subNum * i;
            lastIndex = priIndex + subNum;
            // 判断是否是最后一次
            if (i == insertTimes) {
                subLoanNos = loanNos.subList(priIndex, loanNos.size());
            } else {
                // 非最后一次
                subLoanNos = loanNos.subList(priIndex, lastIndex);

            }
            if (!subLoanNos.isEmpty()) {
                LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
                loanPlanRequest.setLoanNos(subLoanNos);
                try {
                    loanPlanResponses.addAll(lcsFeignService.planDetail(loanPlanRequest));
                } catch (ClientException e) {
                    log.warn("订单根据借据号获取借据信息失败，借据号：{}，原因：{}", loanPlanRequest, e.getMessage());
                }
            }
        }
        return loanPlanResponses;
    }

    private void getProductInfo(List<OrderDto> orderDtos) {
        List<String> subProductCodes = orderDtos.stream().map(OrderDto::getSubProductCode).collect(Collectors.toList());
        List<ProfitProductResponse> productResponseList = productFeignService.loadProfitProductByCondition(subProductCodes);

        FinServiceType finServiceType = redisServiceUtils.getFinBaseInfo();
        if (finServiceType == null) {
            return;
        }
        for (OrderDto orderDto : orderDtos) {
            for (ProfitProductResponse profitProductResponse : productResponseList) {
                if (orderDto.getSubProductCode().equals(profitProductResponse.getProfitCode())) {
                    orderDto.setSubProductName(profitProductResponse.getProfitName());
                    List<ProductBaseInfo> productBaseInfos = finServiceType.getServiceTypeForApp().stream().filter(r -> r.getValue().equals(profitProductResponse.getServiceType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(productBaseInfos) && productBaseInfos.get(0) != null) {
                        orderDto.setServiceType(productBaseInfos.get(0).getLabel());
                    }
                }
            }
        }
    }

    public String queryByOutOrderNumber(GetOrderListRequest request) {
        if (fundFlag) {
            FundOrderQueryResponse response = fundFeignService.queryLoanNewQuery(request.getOutOrderNumber(), null);
            if (response != null) {
                return response.getOrderNumber();
            } else {
                return null;
            }
        } else {
            FundOrderDto respDTO = fundFeignService.queryLoanQuery(request.getOutOrderNumber(), null);
            return respDTO.getOrderNumber();
        }
    }

    public String queryByChannelOrderNumber(GetOrderListRequest request) {
        List<Order> respDTO = fundFeignService.getOrderList(request.getChannelOrderNumber(), null);
        if (!CollectionUtils.isEmpty(respDTO)) {
            return respDTO.get(0).getOrderNumber();
        }
        return "";
    }

    public List<LoanPlanDto> queryBillList(GetBillListRequest request) {
        List<String> loanNos = request.getLoanNos();
        List<LoanPlanResponse> allResponses = new ArrayList<>();
        // 批次大小
        final int BATCH_SIZE = 20;
        for (int i = 0; i < loanNos.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, loanNos.size());
            List<String> batchLoanNos = loanNos.subList(i, endIndex);
            // 创建请求对象
            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setLoanNos(batchLoanNos);
            List<LoanPlanResponse> batchResponses = lcsFeignService.planDetail(loanPlanRequest);
            if (CollectionUtils.isNotEmpty(batchResponses)) {
                allResponses.addAll(batchResponses);
            }
        }
        return getBillDtos(allResponses, request);
    }

    /**
     * 分页查询账单列表
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    public Paging<LoanPlanDto> queryBillListWithPaging(GetBillListPageRequest request) {
        if (CollectionUtils.isEmpty(request.getLoanNos())) {
            return new Paging<>(request.getCurrentPage(), request.getPageSize());
        }

        // 先查询所有借据号，获取实际存在的LoanPlanResponse
        List<String> allLoanNos = request.getLoanNos();
        List<LoanPlanResponse> allResponses = new ArrayList<>();

        // 批次大小
        final int BATCH_SIZE = 20;
        for (int i = 0; i < allLoanNos.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, allLoanNos.size());
            List<String> batchLoanNos = allLoanNos.subList(i, endIndex);
            // 创建请求对象
            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setLoanNos(batchLoanNos);
            List<LoanPlanResponse> batchResponses = lcsFeignService.planDetail(loanPlanRequest);
            if (CollectionUtils.isNotEmpty(batchResponses)) {
                allResponses.addAll(batchResponses);
            }
        }

        // 实际的总数是查询到的LoanPlanResponse数量
        int actualTotalCount = allResponses.size();

        // 计算分页参数
        int currentPage = request.getCurrentPage();
        int pageSize = request.getPageSize();
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, actualTotalCount);

        // 如果起始索引超出范围，返回空结果
        if (startIndex >= actualTotalCount) {
            return new Paging<>(new ArrayList<>(), currentPage, pageSize, actualTotalCount);
        }

        // 从所有响应中取当前页的数据
        List<LoanPlanResponse> pageResponses = allResponses.subList(startIndex, endIndex);

        // 转换为GetBillListRequest以复用现有逻辑
        GetBillListRequest billListRequest = new GetBillListRequest();
        billListRequest.setLoanNos(request.getLoanNos());
        billListRequest.setLoanNoOrderTypes(request.getLoanNoOrderTypes());
        billListRequest.setRpyFlags(request.getRpyFlags());
        billListRequest.setDateDueStart(request.getDateDueStart());
        billListRequest.setDateDueEnd(request.getDateDueEnd());
        billListRequest.setPlanNos(request.getPlanNos());

        // 只处理当前页的数据
        List<LoanPlanDto> pageBillDtos = getBillDtos(pageResponses, billListRequest);

        // 返回分页结果，使用实际的总数
        return new Paging<>(pageBillDtos, currentPage, pageSize, actualTotalCount);
    }

    @Override
    public CanRepayResponse queryCanRepay(CanRepayRequest request) {
        CanRepayResponse canRepayResponse = new CanRepayResponse();
        try {
            if (StringUtils.isNotEmpty(request.getTerm())) {
                RepayLoanCalcResponse response = commonService.getRepayCalculate(1, Collections.singletonList(request.getTerm()), request.getLoanNo(), null);
                if (response != null && response.getRepayCheck() != null) {
                    canRepayResponse.setCanRepay(response.getRepayCheck().getCanRepay());
                }
            }
        } catch (Exception e) {
            log.warn("canRepay repayCalculate failed");
        }

        return canRepayResponse;
    }

    private List<LoanPlanDto> getBillDtos(List<LoanPlanResponse> loanPlans, GetBillListRequest request) {
        List<LoanPlanDto> loanPlanDtos = new ArrayList<>();
        for (LoanPlanResponse loanPlanResponse : loanPlans) {
            List<PlanDto> planDtos = new ArrayList<>();
            for (PlanResponse planResponse : loanPlanResponse.getPlanList()) {
                PlanDto planDto = OrderRequestConverter.INSTANCE.planResponseToPlanDto(planResponse);
                planDtos.add(planDto);
            }
            planDtos = planDtos.stream().sorted(Comparator.comparing(PlanDto::getTerm)).collect(Collectors.toList());

            //账单状态正序，状态类型顺序： 逾期-》未到期-》已结清
            List<PlanDto> realPlanList = new ArrayList<>();
            realPlanList.addAll(planDtos.stream().filter(r -> "1".equals(r.getRpyFlag())).collect(Collectors.toList()));
            realPlanList.addAll(planDtos.stream().filter(r -> "0".equals(r.getRpyFlag())).collect(Collectors.toList()));
            realPlanList.addAll(planDtos.stream().filter(r -> "2".equals(r.getRpyFlag())).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(request.getRpyFlags())) {
                realPlanList = realPlanList.stream().filter(r -> request.getRpyFlags().contains(r.getRpyFlag())).collect(Collectors.toList());
            }

            if (request.getDateDueStart() != null) {
                realPlanList = realPlanList.stream().filter(r -> request.getDateDueStart().isBefore(r.getDateDue())).collect(Collectors.toList());
            }

            if (request.getDateDueEnd() != null) {
                realPlanList = realPlanList.stream().filter(r -> request.getDateDueEnd().isAfter(r.getDateDue())).collect(Collectors.toList());
            }

            LoanPlanDto loanPlanDto = generateLoan(loanPlanResponse, request);
            loanPlanDto.setPlanList(realPlanList);

            loanPlanDtos.add(loanPlanDto);
        }

        return loanPlanDtos;
    }

    private LoanPlanDto generateLoan(LoanPlanResponse loanPlanResponse, GetBillListRequest request) {

        LoanPlanDto loanPlanDto = OrderRequestConverter.INSTANCE.loanPlanResponseToLoanPlanDto(loanPlanResponse);

        if (request.getLoanNoOrderTypes() != null && !request.getLoanNoOrderTypes().isEmpty()) {
            loanPlanDto.setOrderType(request.getLoanNoOrderTypes().get(loanPlanResponse.getLoanNo()));
        }

        if (StringUtils.isNotEmpty(loanPlanResponse.getStatus()) && !"FP".equals(loanPlanResponse.getStatus())) {
            try {
                RepayLoanCalcResponse response = commonService.getRepayCalculate(2, null, loanPlanResponse.getLoanNo(), null);
                //提前结清费
                if (response.getRealAmtDetail() != null && CollectionUtils.isNotEmpty(response.getPlanList())) {
                    BigDecimal fee4 = response.getPlanList().stream()
                            .map(RepayLoanCalcResponse.CalcPlan::getTransDetail)
                            .collect(Collectors.toList()).stream()
                            .map(FeeAmountDto::getTransFee4)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    loanPlanDto.setAdvanceSettlementFee(fee4);
                }

                //减免
                loanPlanDto.setRedDeductAmt(response.getRedDeductAmt());
                //提前结清应还（挡板后）
                loanPlanDto.setRealAmt(response.getRealAmt());
                //提前结清应还
                loanPlanDto.setTotalAmt(response.getTotalAmt());
                //提前结清应还（优惠减免后）
                if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(response.getSettleBaffleScene())) {
                    loanPlanDto.setTotalAmtDiscounts(response.getTotalAmt().subtract(response.getDeductAmt()).subtract(response.getUnprofitDeductAmt()).subtract(response.getRedDeductAmt()));
                } else {
                    loanPlanDto.setTotalAmtDiscounts(response.getTotalAmt().subtract(response.getDeductAmt()).subtract(response.getUnprofitDeductAmt()));
                }

                //挡板后结清费
                if (response.getRealAmtDetail() != null) {
                    loanPlanDto.setSettleAmtLimit(response.getRealAmtDetail().getTransFee4());
                }
            } catch (Exception e) {
                loanPlanDto.setAdvanceSettlementFee(new BigDecimal("-1"));
            }
        }

        return loanPlanDto;
    }

    public Paging<RepaymentsDto> queryRepayments(GetRepaymentsRequest req) {
        Paging<RepaymentsDto> repaymentsDtoPaging = new Paging<>();
        QueryRepaymentsByLoanNoResponse response = repayFacadeClient.getRepayments(req.getLoanNo(), req.getPayStatus(), req.getPayType(), req.getCurrentPage(), req.getPageSize());
        repaymentsDtoPaging.setPageSize(req.getPageSize());
        repaymentsDtoPaging.setCurrentPage(response.getCurrentPage());
        repaymentsDtoPaging.setTotal(response.getTotal());
        repaymentsDtoPaging.setTotalPage(response.getTotalPage());

        List<RepaymentsDto> repaymentsDtos = new ArrayList<>();
        List<BankDto> bankDtos = new ArrayList<>();
        try {
            bankDtos = redisServiceUtils.getBankList();
        } catch (ClientException e) {
            log.warn("获取银行卡列表错误");
        }

        for (QueryRepaymentsByLoanNoResponse.RepaymentInfo repaymentInfo : response.getRepaymentInfos()) {
            RepaymentsDto repaymentsDto = OrderRequestConverter.INSTANCE.repaymentInfoToRepaymentDto(repaymentInfo);
            if (CollectionUtils.isNotEmpty(repaymentsDto.getDeductionInfos()) && repaymentsDto.getDeductionInfos().stream().anyMatch(r -> StringUtils.isNotEmpty(r.getPlanDetailId()))) {
                DeductionInfoDto dto = repaymentsDto.getDeductionInfos().stream().filter(r -> StringUtils.isNotEmpty(r.getPlanDetailId())).findFirst().get();
                RepaymentPlanDetail repaymentPlanDetail = repaymentPlanDetailMapper.selectById(dto.getPlanDetailId());
                if (repaymentPlanDetail != null) {
                    RepaymentPlan repaymentPlan = repaymentPlanMapper.selectById(repaymentPlanDetail.getPlanId());
                    if (repaymentPlan != null) {
                        String name = employeeService.getUserNameForIdentify(repaymentPlan.getCreator());
                        repaymentsDto.setCreator(StringUtils.isNotBlank(name) ? name : dto.getPlanDetailId());
                    } else {
                        repaymentsDto.setCreator("贷后");
                    }
                } else {
                    repaymentsDto.setCreator("贷后");
                }
            }
            if ("reduction".equals(repaymentsDto.getPayTypeCode())) {
                RepayFeeDetailDto successTransDetail = repaymentsDto.getSuccessTransDetail();
                FeeAmountDto feeAmountDto = repaymentInfo.getUnprofitDeductDetail();
                successTransDetail.setTransPrin(feeAmountDto.getTransPrin());
                successTransDetail.setTransInt(feeAmountDto.getTransInt());
                successTransDetail.setGuaranteeFee(feeAmountDto.getTransFee1().add(feeAmountDto.getTransFee2()));
                successTransDetail.setLateFee(feeAmountDto.getTransOint().add(feeAmountDto.getTransFee3()).add(feeAmountDto.getTransFee6()));
            }

            try {
                if (StringUtils.isNotEmpty(repaymentInfo.getBankCardId()) && StringUtils.isNotEmpty(req.getCustNo())) {
                    BankCardResponse bankCardResponse = cisExtFacadeClient.queryBankCardInfoById(req.getCustNo(), Long.parseLong(repaymentInfo.getBankCardId()));
                    if (bankCardResponse != null) {
                        repaymentsDto.setBankCode(bankCardResponse.getBankName());
                        repaymentsDto.setCardNumber(bankCardResponse.getCardNo());
                    }
                } else if (CollectionUtils.isNotEmpty(bankDtos)) {
                    List<String> bankIds = bankDtos.stream().map(BankDto::getBankId).collect(Collectors.toList());
                    if (StringUtils.isNotEmpty(repaymentsDto.getBankCode()) && bankIds.contains(repaymentsDto.getBankCode())) {
                        BankDto bankDto = bankDtos.stream().filter(m -> repaymentsDto.getBankCode().equals(m.getBankId())).findFirst().get();
                        repaymentsDto.setBankCode(bankDto.getName());
                    }
                }
            } catch (ClientException e) {
                log.warn("还款记录获取还款银行信息错误");
            }

            repaymentsDtos.add(repaymentsDto);
        }

        if (isCanCancel) {
            String lastRepaymentNo = findLastRepaymentNo(req);
            if (StringUtils.isNotBlank(lastRepaymentNo)) {
                repaymentsDtos.stream().filter(r -> r.getRepaymentNo().equals(lastRepaymentNo)).findFirst().ifPresent(repaymentsDto -> repaymentsDto.setCanCancel(true));
            }
        }

        repaymentsDtoPaging.setList(repaymentsDtos);
        return repaymentsDtoPaging;
    }

    private String findLastRepaymentNo(GetRepaymentsRequest req) {
        if (req.getLoanNo().size() > 1) {
            return "";
        }

        QueryRepaymentsByLoanNoResponse response;
        int n = 1;
        List<QueryRepaymentsByLoanNoResponse.RepaymentInfo> repaymentInfos = new ArrayList<>();
        while (n <= req.getCurrentPage()) {
            response = repayFacadeClient.getRepayments(req.getLoanNo(), req.getPayStatus(), null, n, req.getPageSize());
            repaymentInfos.addAll(response.getRepaymentInfos());
            n++;
        }

        QueryRepaymentsByLoanNoResponse.RepaymentInfo repaymentInfo = repaymentInfos.stream()
                .filter(r -> "成功".equals(r.getPayStatus()) || "部分成功".equals(r.getPayStatus()))
                .max(Comparator.comparing(QueryRepaymentsByLoanNoResponse.RepaymentInfo::getCreatedTime))
                .orElse(null);

        if (repaymentInfo == null) {
            return "";
        }

        List<ControlData> controlDataLists = UserContextHolder.getUserContext().getControlDataList()
                .stream()
                .filter(r -> r.getControlChildType() == 12 || r.getControlChildType() == 13).collect(Collectors.toList());

        boolean hasPrint = repaymentInfo.getDeductionInfos()
                .stream()
                .filter(r -> r.getStatus().equals("交易成功"))
                .anyMatch(r -> Optional.ofNullable(r.getRealAmountDetail())
                        .map(detail ->
                                detail.getTransPrin().compareTo(BigDecimal.ZERO) > 0 ||
                                        detail.getTransInt().compareTo(BigDecimal.ZERO) > 0)
                        .orElse(false));

        if (controlDataLists.stream().anyMatch(r -> r.getControlChildType() == 12)) {
            return repaymentInfo.getRepaymentNo();
        }

        if (controlDataLists.stream().anyMatch(r -> r.getControlChildType() == 13) && !hasPrint) {
            return repaymentInfo.getRepaymentNo();
        }

        return "";
    }

    /**
     * 查询API导流订单
     *
     * @param apiOrderRequest
     */
    @Override
    public Paging<DiversionOrderDto> queryApiOrderList(ApiOrderRequest apiOrderRequest) {
        // 构建 API 请求对象
        ApiDataDiversionPhpReq apiDataDiversionPhpReq = new ApiDataDiversionPhpReq();
        apiDataDiversionPhpReq.setOrder_no(apiOrderRequest.getOrderNo());
        apiDataDiversionPhpReq.setStatus(apiOrderRequest.getStatus());
        apiDataDiversionPhpReq.setProduct_no(apiOrderRequest.getProductNo());
        apiDataDiversionPhpReq.setApp(apiOrderRequest.getApp());
        apiDataDiversionPhpReq.setPage(apiOrderRequest.getCurrentPage());
        apiDataDiversionPhpReq.setPage_size(apiOrderRequest.getPageSize());

        if (StringUtils.isNotBlank(apiOrderRequest.getMobile())) {
            Long userId = cisFacadeClient.queryIdByMobile(apiOrderRequest.getMobile());
            if (userId != null) {
                apiDataDiversionPhpReq.setUser_id(userId.toString());
            }
        }

        if (StringUtils.isAllBlank(apiDataDiversionPhpReq.getOrder_no(), apiDataDiversionPhpReq.getStatus(),
                apiDataDiversionPhpReq.getProduct_no(), apiDataDiversionPhpReq.getApp(),
                apiDataDiversionPhpReq.getUser_id())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "无有效查询条件");
        }

        ApiDiversionPhpReq apiDiversionPhpReq = ApiDiversionPhpReq.builder()
                .args(apiDataDiversionPhpReq)
                .build();

        ApiDiversionPhpResp apiDiversionPhpResp = apiDiversionPhpFeignClient.queryApiDiversionInfo(apiDiversionPhpReq);

        if (apiDiversionPhpResp == null || !apiDiversionPhpResp.isSuccess() || apiDiversionPhpResp.getResponse() == null) {
            return new Paging<>();
        }

        ApiResponse response = apiDiversionPhpResp.getResponse();
        List<DiversionOrderDto> list = new ArrayList<>();
        Paging<DiversionOrderDto> orderDtoPaging = new Paging<>();

        List<DiversionOrderInfo> listResponse = response.getList();
        List<ProductDetailInfo> productDetailInfoList = new ArrayList<>();
        ProductResponse productResponse = apiDiversionPhpFeignClient.queryProductList(apiDiversionPhpReq);

        if (productResponse != null && productResponse.isSuccess() && productResponse.getResponse() != null) {
            productDetailInfoList = productResponse.getResponse();
        }

        for (DiversionOrderInfo diversionOrderInfo : listResponse) {
            DiversionOrderDto dto = DiversionOrderConverter.INSTANCE.diversionOrderToDiversionOrderDto(diversionOrderInfo);

            if (dto.getAmount() != null) {
                dto.setAmount(new BigDecimal(diversionOrderInfo.getAmount()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }

            if (dto.getAuditAmount() != null) {
                dto.setAuditAmount(new BigDecimal(diversionOrderInfo.getAuditAmount()).movePointLeft(2).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }

            if (CollectionUtils.isNotEmpty(productDetailInfoList) && StringUtils.isNotBlank(diversionOrderInfo.getProductNo())) {
                dto.setProductName(getProductName(productDetailInfoList, diversionOrderInfo.getProductNo()));
            }

            ThreeElementsDTO threeElementsDTO = null;
            if (StringUtils.isNotBlank(diversionOrderInfo.getUserNo()) && !"0".equals(diversionOrderInfo.getUserNo())) {
                threeElementsDTO = cisFacadeClient.queryThreeElementsByUserNo(Long.parseLong(diversionOrderInfo.getUserNo()));
            } else {
                String mobile = cisFacadeClient.queryMobileById(Long.parseLong(diversionOrderInfo.getUserId()));
                UserNoDTO userNoDTO = cisFacadeClient.getUserNoByMobileAndApp(mobile, diversionOrderInfo.getApp());
                if (userNoDTO != null) {
                    threeElementsDTO = cisFacadeClient.queryThreeElementsByUserNo(userNoDTO.getUserNo());
                    dto.setUserNo(userNoDTO.getUserNo().toString());
                }
            }

            if (threeElementsDTO != null) {
                dto.setName(threeElementsDTO.getName());
                dto.setMobile(threeElementsDTO.getMobile());
                dto.setIdNo(threeElementsDTO.getIdNo());
            }

            list.add(dto);
        }

        orderDtoPaging.setList(list);
        orderDtoPaging.setPageSize(response.getPage_info().getPage_size());
        orderDtoPaging.setCurrentPage(response.getPage_info().getPage());
        orderDtoPaging.setTotal(response.getPage_info().getCount());

        return orderDtoPaging;
    }

    private String getProductName(List<ProductDetailInfo> productDetailInfoList, String productNo) {
        return productDetailInfoList.stream()
                .filter(productDetailInfo -> productDetailInfo.getValue().equals(productNo))
                .map(ProductDetailInfo::getName)
                .findFirst()
                .orElse("");
    }

    public List<ProductDetailInfo> queryProductList() {

        ApiDiversionPhpReq apiDiversionPhpReq = ApiDiversionPhpReq.builder().build();
        ApiDataDiversionPhpReq apiDataDiversionPhpReq = new ApiDataDiversionPhpReq();
        apiDataDiversionPhpReq.setOrder_no("1");
        apiDiversionPhpReq.setArgs(apiDataDiversionPhpReq);
        ProductResponse productResponse = apiDiversionPhpFeignClient.queryProductList(apiDiversionPhpReq);
        if (productResponse == null || !productResponse.isSuccess() || productResponse.getResponse() == null) {
            return null;
        }
        return productResponse.getResponse();
    }


    public LegalAgencyDetail queryAgencyDetail(AgencyDetailRequest agencyDetailRequest) {
        LawsuitAgencyQueryReq lawsuitAgencyQueryReq = new LawsuitAgencyQueryReq();
        lawsuitAgencyQueryReq.setLoanNo(agencyDetailRequest.getLoanNo());
        return huTtaLegalClient.queryAgencyDetail(lawsuitAgencyQueryReq);
    }

    public void orderListDownload(HttpServletResponse response, GetBillListRequest request) {
        // 获取账单数据
        List<LoanPlanDto> list = queryBillList(request);
        Map<String, List<OrderListExcelReq>> listExcelMap = new HashMap<>();

        // 遍历 LoanPlanDto 列表并组装数据
        for (LoanPlanDto loanPlanDto : list) {
            List<OrderListExcelReq> listExcel = new ArrayList<>();
            List<String> planNos = request.getPlanNos().get(loanPlanDto.getLoanNo());

            // 遍历 PlanDto 列表，筛选出符合条件的数据
            for (PlanDto planDto : loanPlanDto.getPlanList()) {
                if (CollectionUtils.isNotEmpty(planNos) && planNos.contains(planDto.getPlanNo())) {
                    OrderListExcelReq orderListExcelReq = createOrderListExcelReq(loanPlanDto, planDto);
                    listExcel.add(orderListExcelReq);
                }
            }

            // 如果该 LoanPlanDto 中有符合条件的数据，则加入 listExcelMap
            if (CollectionUtils.isNotEmpty(listExcel)) {
                listExcelMap.put(loanPlanDto.getLoanNo(), listExcel);
            }
        }

        // 获取当前时间并生成文件名
        String time = LocalDateTimeUtils.format(LocalDateTime.now(), "yyyyMMddHHmmss");

        // 如果只有一个文件，直接导出
        if (listExcelMap.size() == 1) {
            exportSingleExcelFile(response, time, listExcelMap);
        } else {
            // 如果有多个文件，打包成 ZIP 文件
            exportZipFile(response, time, listExcelMap);
        }
    }

    private OrderListExcelReq createOrderListExcelReq(LoanPlanDto loanPlanDto, PlanDto planDto) {
        OrderListExcelReq orderListExcelReq = new OrderListExcelReq();
        orderListExcelReq.setTerm(planDto.getTerm());
        orderListExcelReq.setOderNo(loanPlanDto.getLoanNo());
        orderListExcelReq.setRpyFlag(convertRpyFlag(planDto.getRpyFlag()));
        orderListExcelReq.setPlanNo(planDto.getPlanNo());
        orderListExcelReq.setDateCreated(planDto.getDateCreated());
        orderListExcelReq.setDateDue(planDto.getDateDue());
        orderListExcelReq.setDateGrace(planDto.getDateGrace());
        orderListExcelReq.setDateSettle(planDto.getDateSettle());
        orderListExcelReq.setPrinAmt(planDto.getPlanFeeDetail().getInitPlan().getPrinAmt());
        orderListExcelReq.setIntAmt(planDto.getPlanFeeDetail().getInitPlan().getIntAmt());
        orderListExcelReq.setFee1Amt(planDto.getPlanFeeDetail().getInitPlan().getFee1Amt());
        orderListExcelReq.setFee2Amt(planDto.getPlanFeeDetail().getInitPlan().getFee2Amt());
        orderListExcelReq.setSumAmt(planDto.getPlanFeeDetail().getInitPlan().getSumAmt());
        orderListExcelReq.setPrinAmtFact(planDto.getPlanFeeDetail().getActRepayPlan().getPrinAmt());
        orderListExcelReq.setIntAmtFact(planDto.getPlanFeeDetail().getActRepayPlan().getIntAmt());
        orderListExcelReq.setFee4Amt(planDto.getPlanFeeDetail().getActRepayPlan().getFee4Amt());
        orderListExcelReq.setFee1AmtFact(planDto.getPlanFeeDetail().getActRepayPlan().getFee1Amt());
        orderListExcelReq.setFee2AmtFact(planDto.getPlanFeeDetail().getActRepayPlan().getFee2Amt());
        orderListExcelReq.setFee6Amt(planDto.getPlanFeeDetail().getActRepayPlan().getFee6Amt());
        orderListExcelReq.setOintAmt(planDto.getPlanFeeDetail().getActRepayPlan().getOintAmt());
        orderListExcelReq.setFee3Amt(planDto.getPlanFeeDetail().getActRepayPlan().getFee3Amt());
        orderListExcelReq.setSumAmtFact(planDto.getPlanFeeDetail().getActRepayPlan().getSumAmt());
        orderListExcelReq.setDeductSum(planDto.getPlanFeeDetail().getPlanDeduct().getDeductSum());
        return orderListExcelReq;
    }

    private void exportSingleExcelFile(HttpServletResponse response, String time, Map<String, List<OrderListExcelReq>> listExcelMap) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            String fileName = URLEncoder.encode(time, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            for (Map.Entry<String, List<OrderListExcelReq>> entry : listExcelMap.entrySet()) {
                EasyExcel.write(response.getOutputStream(), OrderListExcelReq.class)
                        .sheet(entry.getKey() + "_" + time)
                        .doWrite(entry.getValue());
            }
        } catch (Exception e) {
            log.error("EasyExcel.write error", e);
        }
    }

    private void exportZipFile(HttpServletResponse response, String time, Map<String, List<OrderListExcelReq>> listExcelMap) {
        response.setContentType("application/zip");
        response.setCharacterEncoding("utf-8");

        try {
            String zipFileName = URLEncoder.encode(time + ".zip", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename=" + zipFileName);

            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                for (Map.Entry<String, List<OrderListExcelReq>> entry : listExcelMap.entrySet()) {
                    exportExcelToZip(zipOut, entry.getKey() + "_" + time + ".xlsx", entry.getValue());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void exportExcelToZip(ZipOutputStream zipOut, String fileName, List<OrderListExcelReq> data) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcel.write(byteArrayOutputStream, OrderListExcelReq.class).sheet().doWrite(data);
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOut.putNextEntry(zipEntry);
            zipOut.write(byteArrayOutputStream.toByteArray());
            zipOut.closeEntry();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String convertRpyFlag(String rpyFlag) {
        if (rpyFlag == null) {
            return "未知";
        }

        switch (rpyFlag) {
            case "0":
                return "未到期";
            case "1":
                return "逾期";
            case "2":
                return "本期结清";
            default:
                return "未知";
        }
    }

    /**
     * 提取并去重某个字段的列表
     */
    private List<String> getDistinctValues(List<OrderDto> orderDtos, Function<OrderDto, String> extractor) {
        return orderDtos.stream()
                .filter(orderDto -> extractor.apply(orderDto) != null)
                .map(extractor)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 更新 redis 数据
     */
    private void updateRedis(OrderFilterFactorUnencryptedDto orderFilterFactorDto, int orderSize, List<String> innerAppList,
                             List<String> capitalPoolList, String custNo, Map<String, String> capitalPoolEncrypt) {
        // 更新 redisMap
        orderFilterFactorDto.setOrderSize(orderSize);
        orderFilterFactorDto.setInnerAppList(innerAppList);
        orderFilterFactorDto.setCapitalPoolList(capitalPoolList);
        orderFilterFactorDto.setCapitalPoolEncrypt(capitalPoolEncrypt);

        // 将更新后的 redisMap 存储回 Redis
        redisUtils.set(custNo, JsonUtil.toJson(orderFilterFactorDto), 24, TimeUnit.HOURS);
    }
}