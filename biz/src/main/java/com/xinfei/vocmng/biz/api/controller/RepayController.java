/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.psenginecore.facade.rr.dto.AdvanceOrderListOuterDTO;
import com.xinfei.repaytrade.facade.vo.repay.RepayCancelVO;
import com.xinfei.vocmng.biz.api.RepayApi;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.OperateType;
import com.xinfei.vocmng.biz.model.enums.RepayProcessEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.NPayService;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.*;
import com.xinfei.vocmng.biz.service.RepayService;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ OrderBillController, v 0.1 2023-12-14 14:47 junjie.yan Exp $
 */
@RestController
@LoginRequired
public class RepayController implements RepayApi {

    @Resource
    private RepayService repayService;

    @Resource
    private NPayService nPayService;

    @Override
    @DigestLogAnnotated("repaymentProcess")
    public ApiResponse<RepaymentProcessResp> repaymentProcess(RepaymentProcessReq req) {
        if (!req.getRepayProcessEnums().contains(RepayProcessEnum.INIT)) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "初始枚举必传");
        }
        if (req.getRepayType() == 1 && (req.getLoanNoTerms() == null || req.getLoanNoTerms().isEmpty())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "非结清时，借据号与期数映射关系必传");
        }
        if (req.getRepayType() == 1) {
            for (Map.Entry<String, Integer> entry : req.getLoanNoTerms().entrySet()) {
                if (entry.getValue() <= 0) {
                    throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "非结清时，期数必须大于0");
                }
            }
        }
        return ApiResponse.success(repayService.repaymentProcess(req));
    }

    @Override
    @DigestLogAnnotated("saveRepayPlan")
    @OperateLogAnnotation(type = OperateType.TYPE_SEVEN, description = "还款方案保存/app自助还款")
    public ApiResponse<RepayPlanRequest> saveRepayPlan(RepayPlanRequest request) {
        RepayPlanRequest result = repayService.saveRepayPlan(request);
        return ApiResponse.success(result);
    }

    @Override
    @DigestLogAnnotated("repay")
    @OperateLogAnnotation(type = OperateType.TYPE_SEVEN, description = "聚合支付/系统代扣/线下还款")
    public ApiResponse<Boolean> repay(RepayPlanRequest request) {
        if (request.isNeedReview()) {
            return ApiResponse.success(null);
        }

        if (request.getPlanId() == null) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "执行聚合支付/系统代扣/线下还款必传方案编号");
        }
        return ApiResponse.success(repayService.repay(request));
    }

    @Override
    public ApiResponse<List<FeeRatioProcessResp>> rateCalculation(List<FeeRatioProcessReq> req) {
        for (FeeRatioProcessReq feeRatio: req) {
            if (feeRatio.getTargetFeeRatio() == null && feeRatio.getTargetDeduct() == null) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "目标费率和目标减免金额不能都传空");
            }

            if (feeRatio.getTargetFeeRatio() != null && feeRatio.getTargetDeduct() != null) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "目标费率和目标减免金额不能都传");
            }

            if (feeRatio.getRepayType() == 1 && CollectionUtils.isEmpty(feeRatio.getTerms())) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "非结清时，期数不能传空");
            }
        }

        return ApiResponse.success(repayService.rateCalculation(req));
    }

    @Override
    @DigestLogAnnotated("getChannels")
    public ApiResponse<List<Map<String, Object>>> getChannels() {
        return ApiResponse.success(repayService.getChannels());
    }

    @Override
    @DigestLogAnnotated("loanCalculate")
    public ApiResponse<List<LoanCalculateDto>> loanCalculate(LoanCalculateRequest request) {
        for (LoanInfo loanInfo : request.getLoanInfos()) {
            if (loanInfo.getPlanType() == 1 && (CollectionUtils.isEmpty(loanInfo.getTerms()) || StringUtils.isEmpty(loanInfo.getTotalTerm()))) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "还当期/多期 期数必传");
            }

            if (loanInfo.getPlanType() == 1 && loanInfo.getTerms().contains(loanInfo.getTotalTerm())) {
                loanInfo.setPlanType(2);
            }
        }

        return ApiResponse.success(repayService.loanCalculate(request));
    }

    @Override
    @DigestLogAnnotated("writeOff")
    @OperateLogAnnotation(type = OperateType.TYPE_SEVEN, description = "线下销账")
    public ApiResponse<List<WriteOffResponse>> writeOff(WriteOffRequest request) {

        for (WriteOffLoanDetail writeOffLoanDetail : request.getWriteOffDetails()) {
            if (CollectionUtils.isEmpty(request.getFlowDetails()) && !writeOffLoanDetail.getAmount().equals(BigDecimal.ZERO)) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "应还金额大于0时，必选流水");
            }
            if (writeOffLoanDetail.getPlanType() == 1 && CollectionUtils.isEmpty(writeOffLoanDetail.getTerms())) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "还当期/多期 期数必传");
            }
        }

        return ApiResponse.success(repayService.writeOff(request));
    }

    @Override
    @DigestLogAnnotated("writeOffRecord")
    public ApiResponse<List<WriteOffRecord>> writeOffRecord(WriteOffRecordRequest request) {
        if (CollectionUtils.isEmpty(request.getFlowNos()) && CollectionUtils.isEmpty(request.getFlowNos())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "借据号与流水号都未传");
        }
        return ApiResponse.success(repayService.writeOffRecord(request));
    }

    @Override
    @DigestLogAnnotated("writeOffReversal")
    @OperateLogAnnotation(type = OperateType.TYPE_SEVEN, description = "销账撤销")
    public ApiResponse<Boolean> writeOffReversal(WriteOffReversalRequest request) {
        return ApiResponse.success(repayService.writeOffReversal(request));
    }

    @Override
    @DigestLogAnnotated("repaymentCancel")
    @OperateLogAnnotation(type = OperateType.TYPE_SEVEN, description = "还款撤销")
    public ApiResponse<List<RepayCancelVO>> repaymentCancel(WriteOffReversalRequest request) {
        return ApiResponse.success(repayService.repaymentCancel(request));
    }

    @Override
    @DigestLogAnnotated("repayPlanList")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<PageResultResponse<RepaymentPlanDto>> repayPlanList(RepayPlanListRequest request) {
        request.setIsReview(false);
        return ApiResponse.success(repayService.queryPlanList(request));
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<PageResultResponse<RepaymentPlanDto>> reviewPlanList(RepayPlanListRequest request) {
        request.setIsReview(true);
        return ApiResponse.success(repayService.queryPlanList(request));
    }

    @Override
    @DigestLogAnnotated("repayPlanDetail")
    public ApiResponse<RepayPlanDetailDto> repayPlanDetail(PlanDetailRequest request) {
        return ApiResponse.success(repayService.queryPlanDetails(request));
    }

    @Override
    public ApiResponse<HuttaPlanDetailDto> huttaDetail(HuttaDetailRequest request) {
        return ApiResponse.success(repayService.queryHuttaPlanDetail(request));
    }

    @Override
    @DigestLogAnnotated("repayPlanInvalid")
    @OperateLogAnnotation(type = OperateType.TYPE_SEVEN, description = "还款方案失效")
    public ApiResponse<List<PlanInvalidResponse>> repayPlanInvalid(List<PlanInvalidRequest> request) {
        if (CollectionUtils.isEmpty(request)) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "方案失效列表必传");
        }
        return ApiResponse.success(repayService.repayPlanInvalid(request));
    }

    @Override
    @DigestLogAnnotated("deduction")
    public ApiResponse<List<DeductionLoansResponse>> deductionLoans(DeductionLoansRequest request) {
        return ApiResponse.success(repayService.deductionLoans(request));
    }

    @Override
    public ApiResponse<DeductionAmtResponse> deductionAmt(DeductionAmtRequest request) {
        return ApiResponse.success(repayService.deductionAmt(request));
    }

    @Override
    public ApiResponse<PageResultResponse<BankFlow>> bankFlow(BankFlowRequest request) {
        return ApiResponse.success(repayService.bankFlow(request));
    }

    @Override
    public ApiResponse<Boolean> updateRepayMethod(RepayMethodRequest request) {
        return ApiResponse.success(repayService.updateRepayMethod(request));
    }

    @Override
    public ApiResponse<Paging<RepayReductionDto>> deductionList(RepayTradeDeductionRequest request) {
        if (request.getLoanNo().isEmpty()) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "借据号不能为空");
        }
        return ApiResponse.success(repayService.deductionList(request));
    }

    @Override
    public ApiResponse<Boolean> deductionCancel(RepayTradeDeductionCancelRequest request) {
        return ApiResponse.success(repayService.deductionCancel(request));
    }

    @Override
    public ApiResponse<ReduceCalculateResponse> reduceCalculate(ReduceCalculateReq request) {
        return ApiResponse.success(repayService.reduceCalculate(request));
    }

    @Override
    public ApiResponse<List<AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO>> queryAdvanceOrderList(AdvanceOrderListReq req) {
        return ApiResponse.success(repayService.queryAdvanceOrderList(req));
    }

    public ApiResponse<List<ReduceCalculateResponse>> reduceCalculateMany(List<ReduceCalculateReq> request) {
        return ApiResponse.success(repayService.reduceCalculateMany(request));
    }

    public ApiResponse<List<ReduceCalculateVerticalResponse>> reduceCalculateVertical(List<ReduceCalculateVerticalReq> request) {
        return ApiResponse.success(repayService.reduceCalculateVertical(request));
    }
}