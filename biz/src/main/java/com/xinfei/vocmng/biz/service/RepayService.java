package com.xinfei.vocmng.biz.service;

import com.xinfei.psenginecore.facade.rr.dto.AdvanceOrderListOuterDTO;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResultResponse;
import com.xinfei.repaytrade.facade.vo.repay.RepayCancelVO;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.*;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;

import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public interface RepayService {

    RepaymentProcessResp repaymentProcess(RepaymentProcessReq req);

    RepayPlanRequest saveRepayPlan(RepayPlanRequest request);

    Boolean repay(RepayPlanRequest request);

    List<FeeRatioProcessResp> rateCalculation(List<FeeRatioProcessReq> req);

    List<Map<String, Object>> getChannels();

    List<LoanCalculateDto> loanCalculate(LoanCalculateRequest request);

    List<WriteOffResponse> writeOff(WriteOffRequest request);

    List<WriteOffRecord> writeOffRecord(WriteOffRecordRequest request);

    Boolean writeOffReversal(WriteOffReversalRequest request);

    List<RepayCancelVO> repaymentCancel(WriteOffReversalRequest request);

    PageResultResponse<RepaymentPlanDto> queryPlanList(RepayPlanListRequest request);

    RepayPlanDetailDto queryPlanDetails(PlanDetailRequest request);

    HuttaPlanDetailDto queryHuttaPlanDetail(HuttaDetailRequest request);

    List<PlanInvalidResponse> repayPlanInvalid(List<PlanInvalidRequest> requests);

    void updateRepaymentPlanStatus(List<Long> updateInfos, Integer status);

    void updateDetail(QueryRepaymentPlanResultResponse.PlanDetailResult planDetailResult);

    void updateRepaymentAllStatus();

    void updateRepaymentSuccess();

    void updatePending();

    List<RepaymentPlanDetail> queryInvalidData();

    List<DeductionLoansResponse> deductionLoans(DeductionLoansRequest request);

    DeductionAmtResponse deductionAmt(DeductionAmtRequest req);

    PageResultResponse<BankFlow> bankFlow(BankFlowRequest request);

    Boolean updateRepayMethod(RepayMethodRequest request);

    Paging<RepayReductionDto> deductionList(RepayTradeDeductionRequest request);

    Boolean deductionCancel(RepayTradeDeductionCancelRequest request);

    ReduceCalculateResponse reduceCalculate(ReduceCalculateReq request);

    List<ReduceCalculateResponse> reduceCalculateMany(List<ReduceCalculateReq> request);

    List<ReduceCalculateVerticalResponse> reduceCalculateVertical(List<ReduceCalculateVerticalReq> request);

    List<AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO> queryAdvanceOrderList(AdvanceOrderListReq req);
}
