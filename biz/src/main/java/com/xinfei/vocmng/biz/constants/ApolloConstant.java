package com.xinfei.vocmng.biz.constants;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version $ ApolloConstant, v 0.1 2025/5/9 10:57 shaohui.chen Exp $
 */
@Configuration
public class ApolloConstant {
    @ApolloJsonValue("${cust.merge.enabled:false}")
    public static Boolean mergeQueueEnabled;

    @ApolloJsonValue("${cust.duplicate.merge.enabled:true}")
    public static Boolean mergeDuplicateEnabled;

    @ApolloJsonValue("${cust.push.enabled:true}")
    public static Boolean pushQueueEnabled;

    @ApolloJsonValue("${sms.ivr.loan.templateId:smstpl712212}")
    public static String smsIvrLoanTemplateId;

    @ApolloJsonValue("${intermediary.contract.title:居间服务协议}")
    public static String INTERMEDIARY_CONTRACT_TITLE;

    @ApolloJsonValue("${intermediary.contract.shortName:xyf264}")
    public static String INTERMEDIARY_CONTRACT_SHORT_NAME;

    @ApolloJsonValue("${work.order.user.id:600}")
    public static Long WORK_ORDER_USER_ID;

    @ApolloJsonValue("${auto.work.order:false}")
    public static Boolean AUTO_WORK_ORDER;

    @ApolloJsonValue("${auto.work.crowdId:2000}")
    public static Long AUTO_WORK_CROWDID;
}
