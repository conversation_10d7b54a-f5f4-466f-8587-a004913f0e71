package com.xinfei.vocmng.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinfei.vocmng.dal.dto.req.EmployeeComReq;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.EmployeeComResp;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.Role;
import com.xinfei.vocmng.dal.provider.EmployeeSqlProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * <p>
 * 员工表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Mapper
public interface EmployeeMapper extends BaseMapper<Employee> {


    @Select("SELECT  a.name,a.id " +
            "FROM role a LEFT JOIN employee_role_mapping b ON a.id = b.role_id " +
            "WHERE b.user_identify = #{userIdentify} and a.is_deleted = 0 and b.is_deleted =0 limit 1")
    Role getUserWithRolesByUserIdentify(@Param("userIdentify") String userIdentify);

    @Select("SELECT c.data " +
            "FROM employee_role_mapping a " +
            "JOIN role_data_auth_mapping b ON a.role_id = b.role_id " +
            "JOIN data_auth c ON b.data_id = c.id " +
            "WHERE a.user_identify = #{userIdentify} and a.is_deleted=0 and b.is_deleted=0 and c.is_deleted=0")
    List<String> getUserWithDataByUserIdentify(@Param("userIdentify") String userIdentify);

    @Select("SELECT c.permission_identify " +
            "FROM employee_role_mapping a " +
            "JOIN resource_role_mapping b ON a.role_id = b.role_id " +
            "JOIN resource c ON b.resource_id = c.id " +
            "WHERE a.user_identify = #{userIdentify} and a.is_deleted=0 and b.is_deleted=0 and c.is_deleted=0")
    List<String> getUserWithResourceByUserIdentify(@Param("userIdentify") String userIdentify);


    List<String> getUserIdentifyByUserName(@Param("userNames") List<String> userNames);

    @Select("SELECT  user_identify " +
            "FROM employee " +
            "WHERE name = #{name} and is_deleted = 0 and state = 0")
    List<String> getUserIdentifyByName(@Param("name") String name);

    @Select("SELECT  department_id,name,id,real_time_assistance,sso_user_id " +
            "FROM employee " +
            "WHERE user_identify = #{userIdentify} and is_deleted = 0 and state = 0")
    Employee getDepartmentIdByUserIdentify(@Param("userIdentify") String userIdentify);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "findNotExistingRoleNames")
    List<String> findNotExistingRoleNames(@Param("names") List<String> names);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "findNotExistingDepartNames")
    List<String> findNotExistingDepartNames(@Param("names") List<String> names);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "findNotExistingLabelType")
    List<String> findNotExistingLabelType(@Param("names") List<String> names);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "findNotExistingLabelName")
    List<String> findNotExistingLabelName(@Param("names") List<String> names);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "getControleData")
    List<ControlData> getControleData(@Param("userIdentify")String userIdentify);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "getFeeStrategyConfig")
    List<FeeStrategyConfig> getFeeStrategyConfig(@Param("userIdentify")String userIdentify);

    @SelectProvider(type = EmployeeSqlProvider.class, method = "selectEmployeeResultsByConditionsEncry")
    IPage<EmployeeComResp> selectEmployeeResultsByConditionsEncry(@Param("query")EmployeeComReq query, Page<EmployeeComResp> page);

    /**
     * 根据用户加密手机号查询员工信息信息
     * @param userEncodePhones 加密手机号
     * @return List<Employee>
     */
    List<Employee> getEmployeeByUserEncodePhones(@Param("userEncodePhones") List<String> userEncodePhones);


    List<Employee> selectByUserIdentifyIn(@Param("userIdentifies") List<String> userIdentifies);

}
