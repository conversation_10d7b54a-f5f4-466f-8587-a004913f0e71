<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.vocmng</groupId>
        <artifactId>vocmng</artifactId>
        <version>1.0.4.20230904</version>
    </parent>

    <artifactId>util</artifactId>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!--xfframework-->
        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-dependency</artifactId>
        </dependency>
        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.arms.apm</groupId>
            <artifactId>arms-sdk</artifactId>
            <version>${arms.apm.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-starter-mq</artifactId>
            <version>${xfframework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.ssocore</groupId>
            <artifactId>ssocore-client</artifactId>
            <version>${ssocore.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${net.logstash.logback.version}</version>
        </dependency>


    </dependencies>

</project>
