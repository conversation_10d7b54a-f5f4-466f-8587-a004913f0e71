/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.service;

import com.xinfei.ssomng.facade.api.SsoUserFacade;
import com.xinfei.ssomng.facade.rr.ApiResponse;
import com.xinfei.ssomng.facade.rr.request.UserInfoApiRequest;
import com.xinfei.ssomng.facade.rr.request.UserInfoQueryApiRequest;
import com.xinfei.ssomng.facade.rr.request.UserInfoStatusUpdateApiRequest;
import com.xinfei.ssomng.facade.rr.request.UserInfoUpdateApiRequest;
import com.xinfei.ssomng.facade.rr.response.UserInfoApiResponse;
import com.xinfei.ssomng.facade.utils.SignUtils;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.dto.SecureEncryptDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientService, v 0.1 2024-03-08 15:56 junjie.yan Exp $
 */
@Slf4j
@Service
public class CisFacadeClientService {
    @Autowired
    private CisFacadeClient cisFacadeClient;
    @Autowired
    private SsoUserFacade  ssoUserFacade;

    public String getEncodeMobileLocal(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return "";
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptLocal(Collections.singletonList(mobile));
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) && secureEncryptDTOS.get(0) != null) {
            return secureEncryptDTOS.get(0).getCipherText();
        }
        return "";
    }

    public List<String> getEncodeMobileLocal(List<String> text) {
        if (CollectionUtils.isEmpty(text)) {
            return Collections.emptyList();
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptLocal(text);
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS)) {
            return secureEncryptDTOS.stream().map(SecureEncryptDTO::getCipherText).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public Map<String,String> getEncodeMobileMap(List<String> cipherText) {
        if (CollectionUtils.isEmpty(cipherText)) {
            return Collections.emptyMap();
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptLocal(cipherText);
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) ) {
            return secureEncryptDTOS.stream()
                    .collect(Collectors.toMap(
                            SecureEncryptDTO::getCipherText,
                            SecureEncryptDTO::getPlainText));
        }
        return Collections.emptyMap();
    }

    public Map<String,String> getEncodeMobileMapKey(List<String> cipherText) {
        if (CollectionUtils.isEmpty(cipherText)) {
            return Collections.emptyMap();
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptLocal(cipherText);
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) ) {
            return secureEncryptDTOS.stream()
                    .filter(dto -> dto.getPlainText() != null)
                    .collect(Collectors.toMap(
                            SecureEncryptDTO::getPlainText,
                            SecureEncryptDTO::getCipherText,
                            (existingValue, newValue) -> existingValue));
        }
        return Collections.emptyMap();
    }

    //mobile手机号,idCard身份证,name姓名，bankcard银行卡,common_property 通用字段（随便传字符串进行加密）
    public String getEncodeByField(String field, List<String> plainTexts) {
        if (StringUtils.isEmpty(field) || CollectionUtils.isEmpty(plainTexts)) {
            return "";
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptByField(field, plainTexts);
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) && secureEncryptDTOS.get(0) != null) {
            return secureEncryptDTOS.get(0).getCipherText();
        }
        return "";
    }

    public String batchDecrypt(String cipherText) {
        if (StringUtils.isEmpty(cipherText)) {
            return "";
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchDecrypt(Collections.singletonList(cipherText), "mobile");
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) && secureEncryptDTOS.get(0) != null) {
            return secureEncryptDTOS.get(0).getPlainText();
        }
        return "";
    }

    public Map<String,String> batchDecrypt(List<String> cipherText) {
        if (CollectionUtils.isEmpty(cipherText)) {
            return Collections.emptyMap();
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchDecrypt(cipherText, "mobile");
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) ) {
            return secureEncryptDTOS.stream()
                    .collect(Collectors.toMap(
                            SecureEncryptDTO::getCipherText,
                            SecureEncryptDTO::getPlainText));
        }
        return Collections.emptyMap();
    }

    public String getDecryptByField(String field, List<String> plainTexts) {
        if (StringUtils.isEmpty(field) || CollectionUtils.isEmpty(plainTexts)) {
            return "";
        }
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchDecrypt(plainTexts, field);
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) && secureEncryptDTOS.get(0) != null) {
            return secureEncryptDTOS.get(0).getPlainText();
        }
        return "";
    }

    public Triple<Boolean, String, Long> syncUserData(String name, String mobile, String identify, String createName, Integer status) {
        Long id;
        UserInfoApiRequest u = new UserInfoApiRequest();
        //u.setUserName(name);
        u.setFullName(name);
        u.setMobile(mobile);
        u.setSourceType(3);
        u.setUserStatus(status);
        u.setCreateBy(createName);
        u.setOldUserId(identify);
        u.setMyAppCode("vocmng");
        u.setSign(SignUtils.createSign(u));
        try {
            ApiResponse<UserInfoApiResponse> res = ssoUserFacade.register(u);
            log.info("sso register log ,req:{},resp:{}", JsonUtil.toJson(u), JsonUtil.toJson(res));
            if (!res.isSuc() || Objects.isNull(res.getData()) || Objects.isNull(res.getData().getUserId())) {
                return Triple.of(Boolean.FALSE, res.getErrorContext().getErrDesc(), null);
            }
            id = res.getData().getUserId();
        } catch (Exception e) {
            log.warn("sso register error ,req:{}", JsonUtil.toJson(u), e);
            return Triple.of(Boolean.FALSE, "sso register error", null);
        }
        return Triple.of(Boolean.TRUE, "", id);
    }

    public void updateUserStatus(Long uid, Integer status, String updateName) {
        UserInfoStatusUpdateApiRequest u = new UserInfoStatusUpdateApiRequest();
        u.setUserId(uid);
        u.setUserStatus(status);
        u.setUpdateBy(updateName);
        u.setMyAppCode("vocmng");
        u.setSign(SignUtils.createSign(u));
        try {
            ApiResponse<Void> res = ssoUserFacade.updateUserStatus(u);
            if (!res.isSuc()) {
                log.warn("sso updateUserStatus error ,req:{},resp:{}", JsonUtil.toJson(u),JsonUtil.toJson(res));
                throw new ClientException("sso updateUserStatus error");
            }
        }catch (Exception e){
            log.warn("sso updateUserStatus error");
            throw new ClientException("sso updateUserStatus error");
        }

    }

    public void updateSsoUser(Long uid, String name,String mobiel, String updateName) {
        UserInfoUpdateApiRequest u = new UserInfoUpdateApiRequest();
        u.setUserId(uid);
        u.setFullName(name);
        u.setMobile(mobiel);
        u.setUpdateBy(updateName);
        u.setMyAppCode("vocmng");
        u.setSign(SignUtils.createSign(u));
        try {
            ApiResponse<Void> res =  ssoUserFacade.update(u);
            if (!res.isSuc()) {
                log.warn("sso updateUser error ,req:{},resp:{}", JsonUtil.toJson(u),JsonUtil.toJson(res));
                throw new ClientException("sso updateUser error");
            }
        }catch (Exception e){
            log.warn("sso updateUser error");
            throw new ClientException("sso updateUser error");
        }

    }

    public Integer getSsoUserSourceType(Long uid){
        try {
            UserInfoQueryApiRequest r = new UserInfoQueryApiRequest();
            r.setUserId(uid);
            r.setMyAppCode("vocmng");
            r.setSign(SignUtils.createSign(r));
            ApiResponse<UserInfoApiResponse> res =  ssoUserFacade.detail(r);
            if (!res.isSuc() || Objects.isNull(res.getData()) || Objects.isNull(res.getData().getSourceType())) {
                log.warn("sso detail error ,req:{},resp:{}", uid,JsonUtil.toJson(res));
                throw new ClientException("sso getSsoUserSourceType error");
            }
            return res.getData().getSourceType();
        }catch (Exception e){
            log.warn("sso getSsoUserSourceType error");
            throw new ClientException("sso getSsoUserSourceType error");
        }
    }


}