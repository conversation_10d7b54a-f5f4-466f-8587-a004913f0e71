/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import apollo.com.google.common.cache.CacheBuilder;
import apollo.com.google.common.cache.CacheLoader;
import apollo.com.google.common.cache.LoadingCache;
import cn.hutool.core.util.IdUtil;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.KmUdeskClient;
import com.xinfei.vocmng.itl.client.http.UdeskClient;
import com.xinfei.vocmng.itl.rr.AgentsResponse;
import com.xinfei.vocmng.itl.rr.CalllogsResponse;
import com.xinfei.vocmng.itl.rr.CreateCustomerResponse;
import com.xinfei.vocmng.itl.rr.Customer;
import com.xinfei.vocmng.itl.rr.ExportCustomerResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerFilterListResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerListResponse;
import com.xinfei.vocmng.itl.rr.ImCallLogQueryResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsListQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsResponse;
import com.xinfei.vocmng.itl.rr.MergeCustomerResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsQueryResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsRequest;
import com.xinfei.vocmng.itl.rr.SessionsVoteResponse;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class UdeskClientService {
    @Resource
    private UdeskClient udeskClient;
    @Resource
    private KmUdeskClient kmUdeskClient;

    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数 (不包括首次尝试)
    private static final long INITIAL_BACKOFF_MS = 1000; // 初始等待时间 (毫秒)
    private static final double BACKOFF_MULTIPLIER = 2.0; // 等待时间乘数
    private static final long MAX_BACKOFF_MS = 30000; // 最大等待间隔，防止无限增长
    private static final int UDESK_SUCCESS_CODE = 1000; // 假设UDesk API调用成功的业务码
    private static final int UDESK_USER_NOT_FOUND_CODE = 2005;
    private static final int UDESK_RATE_LIMIT_CODE = 429;
    private static final Random random = new Random();
    private static final String signVersion = "v2";
    private static final String email = "<EMAIL>";
    private static final String openApi = "ea0a74f8-f767-4f4b-bbe1-df88bbce89a3";
    private static final String apiToken = "ZOxq97uay5mgzZ2lC5PJicENboNy6J5bYvvYa06";
    private static final String ALL_USER_GROUPS_KEY = "allUserGroups";
    private static final String ALL_AGENTS_KEY = "allAgents";


    // 定义 LoadingCache，10分钟过期，现阶段客服人数比较少，暂时放在内存中做缓存
    private final LoadingCache<String, List<ImUserGroupsResponse>> userGroupsCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<ImUserGroupsResponse>>() {
                @Override
                public List<ImUserGroupsResponse> load(String key) throws Exception {
                    return loadAllUserGroups();
                }
            });

    // 定义 LoadingCache，缓存 key 固定为 "allAgents"，10分钟过期
    private final LoadingCache<String, List<AgentsResponse>> agentsCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<AgentsResponse>>() {
                @Override
                public List<AgentsResponse> load(String key) throws Exception {
                    return loadAllAgents();
                }
            });

    public Integer customerCallLogs(String mobile) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Integer nonce = random.nextInt(999999);
        LocalDate endTime = LocalDate.now();
        LocalDate startTime = endTime.plusDays(-7);
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        CalllogsResponse response = null;
        String msg = "UDeskClient.customerCallLogs:";
        try {
            response = udeskClient.customerCallLogs(ts, sign, mobile, nonce, signVersion, email, startTime, endTime);
            log.info(LogUtil.clientLog("UDeskClient", "customerCallLogs", mobile, response));
            if (Objects.isNull(response) || response.getCode() != 1000 || !"成功".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getCodeMessage();
                throw new Exception(msg);
            }
            return response.getSize();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "customerCallLogs", mobile, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Integer customerImLogs(String mobile) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Integer nonce = random.nextInt(999999);
        LocalDate endTime = LocalDate.now();
        LocalDate startTime = endTime.plusDays(-7);
        //<EMAIL>&ea0a74f8-f767-4f4b-bbe1-df88bbce89a3&1705393146&23&v2
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        CalllogsResponse response = null;
        String msg = "UDeskClient.customerImLogs:";
        try {
            response = udeskClient.customerImLogs(email, signVersion, nonce, sign, ts, startTime.toString(), endTime.toString(), 1, "cellphone", mobile);
            log.info(LogUtil.clientLog("UDeskClient", "customerImLogs", mobile, response));
            if (Objects.isNull(response) || response.getStatus() != 0 || !"成功".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                if (response != null && "对不起，该资源不存在".equals(response.getMessage())) {
                    log.warn(LogUtil.clientErrorLog("UDeskClient", "customerImLogs", mobile, response, msg));
                    return 0;
                }
                throw new Exception(msg);
            }
            return response.getTotal();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "customerImLogs", mobile, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    public AgentsResponse getAgent(String id) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Integer nonce = random.nextInt(999999);
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        CalllogsResponse response = null;
        String msg = "UDeskClient.getAgent:";
        try {
            response = udeskClient.getAgent(email, signVersion, nonce, sign, ts, "id", id);
            log.info(LogUtil.clientLog("UDeskClient", "getAgent", id, response));
            if (Objects.isNull(response) || response.getCode() != 1000 || StringUtils.isEmpty(response.getAgent().getCellphone())) {
                msg += response == null ? "response is null" : response.getCodeMessage();
                if (response == null || response.getAgent() == null || StringUtils.isEmpty(response.getAgent().getCellphone())) {
                    msg += "此坐席id无对应手机号，坐席id：" + id;
                }
                throw new Exception(msg);
            }
            return response.getAgent();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "getAgent", id, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ImSessionsListQueryResponse sessionsSync(Integer pageSize, Integer page, String startTime, String endTime) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = System.currentTimeMillis() + random.nextInt(999999);
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        ImSessionsListQueryResponse response = null;
        String msg = "UDeskClientError.sessionsSync 对话列表查询: 查询时间段 " + startTime + " 到 " + endTime;
        try {
            response = udeskClient.sessionsSync(email, signVersion, nonce, sign, ts, page, pageSize, startTime, endTime);
            log.info(LogUtil.clientLog("UDeskClient", "sessionsSync", page, response));
            if (Objects.isNull(response) || response.getStatus() != 0) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "sessionsSync", page, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ImSessionsDetailsQueryResponse sessionDetailsSync(String imSubSessionId, String startTime, String endTime) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        ImSessionsDetailsQueryResponse response = null;
        String msg = "UDeskClientError.sessionDetailsSync 对话详情查询: imSubSessionId" + imSubSessionId + " 查询时间段 " + startTime + " 到 " + endTime;

        try {
            response = udeskClient.sessionDetailsSync(email, signVersion, nonce, sign, ts, imSubSessionId);
            log.info(LogUtil.clientLog("UDeskClient", "getAgent", imSubSessionId, response));
            if (Objects.isNull(response) || response.getStatus() != 0) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "sessionDetailsSync", imSubSessionId, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ImCallLogQueryResponse callSync(Integer per_page, Integer page, String startTime, String endTime) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        ImCallLogQueryResponse response = null;
        String msg = "UDeskClientError.callSync: 通话记录查询 查询时间段 " + startTime + " 到 " + endTime;
        try {
            response = udeskClient.callSync(email, signVersion, nonce, sign, ts, page, per_page, startTime, endTime);
            log.info(LogUtil.clientLog("UDeskClient", "callSync", page, response));
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "callSync", page, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ImCallLogQueryResponse callDetailsSync(String callId) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        ImCallLogQueryResponse response = null;
        String msg = "UDeskClientError.callSync: 通话记录详情查询 callId： "+callId;
        try {
            response = udeskClient.callDetailsSync(email, signVersion, nonce, sign, ts, callId);
            log.info(LogUtil.clientLog("UDeskClient", "callDetailsSync", callId, response));
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "callDetailsSync", callId, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public RobotSessionsQueryResponse robotSync(RobotSessionsRequest robotSessionsRequest) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        String value = email + "&" + apiToken + "&" + ts;
        String sign = DigestUtils.sha1Hex(value);
        RobotSessionsQueryResponse response = null;
        String msg = "kmUdeskClientError.robotSync: 机器人对话列表查询 查询时间段 " + robotSessionsRequest.getStartTime() + " 到 " + robotSessionsRequest.getEndTime();
        try {
            response = kmUdeskClient.robotSync(email, ts, sign, robotSessionsRequest);
            log.info(LogUtil.clientLog("kmUdeskClient", "robotSync", robotSessionsRequest, response));
            if (Objects.isNull(response) || response.getCode() != 200 || !"OK".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("kmUdeskClient", "robotSync", robotSessionsRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public RobotSessionsDetailsQueryResponse robotDetailsSync(Long sessionId, Integer pageNum, Integer pageSize, String startTime, String endTime) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        String value = email + "&" + apiToken + "&" + ts;
        String sign = DigestUtils.sha1Hex(value);

        RobotSessionsDetailsQueryResponse response = null;
        String msg = "kmUdeskClientError.robotDetailsSync 机器人详情查询 sessionId:" + sessionId + " 查询时间段 " + startTime + " 到 " + endTime;
        try {
            response = kmUdeskClient.robotDetailsSync(sessionId, email, ts, sign, pageNum, pageSize);
            log.info(LogUtil.clientLog("kmUdeskClient", "robotDetailsSync", sessionId, response));
            if (Objects.isNull(response) || response.getCode() != 200 || !"OK".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("kmUdeskClient", "robotDetailsSync", sessionId, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ImCustomerDetailsQueryResponse getCustomerDetails(String type, String content) {
        /** type-条件类型
         id	客户id
         email	客户邮箱
         cellphone	客户电话
         token	客户外部唯一标识
         weixin_open_id	客户微信openid
         weixin_mini_openid	客户微信小程序openid
         weixin_work_identifier	客户企业微信的唯一标识，例：cropid:wxc727955fe6025ed4,agentid:1009117,userid:LS004308
         weibo_id	客户微博openid
         sdk_token	客户sdk标识
         web_token	客户web标识
         **/
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        ImCustomerDetailsQueryResponse response = null;
        String msg = "UDeskClientError.getCustomerDetails 客户详情: 查询内容 type:" + type + ",content:" + content;
        try {
            response = udeskClient.getCustomerDetails(type, content, email, ts, sign, nonce, signVersion);
            log.info(LogUtil.clientLog("UDeskClient", "getCustomerDetails", type, content));
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += "客户详情获取异常";
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "getCustomerDetails", response, msg, e));
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ImCustomerDetailsQueryResponse getCustomerDetailsWithOutException(String type, String content){
        try {
            return getCustomerDetails(type, content);
        } catch (Exception e) {
            // do nothing
            return null;
        }
    }

    /**
     *  此方法会处理用户查询不到和限流的情况
     */
    public ImCustomerDetailsQueryResponse getCustomerDetailsNew(String type, String content) {
        String baseMsg = "UDeskClientError.getCustomerDetailsNew 客户详情: 查询内容 type:" + type + ",content:" + content;
        ImCustomerDetailsQueryResponse response = null;

        long currentBackoffMs = INITIAL_BACKOFF_MS;
        int attempt = 0;

        while (attempt <= MAX_RETRY_ATTEMPTS) {
            attempt++;
            Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
            Long nonce = IdUtil.getSnowflakeNextId();
            String valueToSign = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
            String sign = DigestUtils.sha1Hex(valueToSign);

            String attemptMsg = baseMsg + " (Attempt " + attempt + "/" + (MAX_RETRY_ATTEMPTS + 1) + ")";

            try {
                log.info(LogUtil.clientLog("UDeskClient", "getCustomerDetailsNew", "Attempt: " + attempt, type, content));
                 response = udeskClient.getCustomerDetails(type, content, email, ts, sign, nonce, signVersion);
                if (Objects.isNull(response)) {
                    log.error(LogUtil.clientErrorLog("UDeskClient", "getCustomerDetailsNew", null, attemptMsg + " -> API返回null响应", null));
                    throw new Exception(attemptMsg + " -> API返回null响应");
                }

                log.info("UDesk API response code: {} for type: {}, content: {}", response.getCode(), type, content);

                if (response.getCode() == UDESK_SUCCESS_CODE) {
                    return response;
                } else if (response.getCode() == UDESK_USER_NOT_FOUND_CODE) {
                    log.info("User not found (Code {}) for type: {}, content: {}. No retry needed.", UDESK_USER_NOT_FOUND_CODE, type, content);
                    return null;
                } else if (response.getCode() == UDESK_RATE_LIMIT_CODE) {
                    log.warn("Rate limit hit (Code {}). Attempt {}/{}. For type: {}, content: {}",
                            UDESK_RATE_LIMIT_CODE, attempt, MAX_RETRY_ATTEMPTS + 1, type, content);

                    if (attempt > MAX_RETRY_ATTEMPTS) {
                        log.error("Max retries ({}) exceeded for rate limit. type: {}, content: {}", MAX_RETRY_ATTEMPTS, type, content);
                        throw new ClientException(TechplayErrDtlEnum.RATE_LIMIT_EXCEEDED,
                                attemptMsg + " -> 达到最大重试次数的限流错误", ErrorLevelsEnum.ERROR);
                    }

                    try {
                        log.info("Waiting {} ms before next retry for rate limit...", currentBackoffMs);
                        Thread.sleep(currentBackoffMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // 恢复中断状态
                        log.warn("Retry delay interrupted for rate limit. type: {}, content: {}", type, content);
                        throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, attemptMsg + " -> 重试等待被中断", ErrorLevelsEnum.ERROR);
                    }
                    // 更新下次等待时间 (指数退避 + 抖动)
                    currentBackoffMs = Math.min((long) (currentBackoffMs * BACKOFF_MULTIPLIER), MAX_BACKOFF_MS);
                    currentBackoffMs += ThreadLocalRandom.current().nextLong(INITIAL_BACKOFF_MS / 2); // 添加抖动，防止同时重试
                } else {
                    String udeskErrorMsg = attemptMsg + " -> UDesk返回非成功或特定处理的错误码: " + response.getCode();
                    throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, udeskErrorMsg, ErrorLevelsEnum.ERROR);
                }

            } catch (ClientException ce) {
                throw ce;
            } catch (Exception e) { // 捕获其他所有异常，例如网络问题、签名问题等
                log.error(LogUtil.clientErrorLog("UDeskClient", "getCustomerDetailsNew", response, attemptMsg, e));
                throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, attemptMsg + " -> 发生未知异常: " + e.getMessage(), ErrorLevelsEnum.ERROR);
            }
        }
        log.error("Exited retry loop unexpectedly for type: {}, content: {}", type, content);
        throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, baseMsg + " -> 未知原因导致重试循环结束", ErrorLevelsEnum.ERROR);
    }


    public ImUserGroupsQueryResponse userGroups(Integer pageSize, Integer page) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        ImUserGroupsQueryResponse response = null;
        String msg = "UDeskClientError.userGroups 客服组列表查询";
        try {
            response = udeskClient.userGroups(email, signVersion, nonce, sign, ts, page, pageSize);
            log.info(LogUtil.clientLog("UDeskClient", "userGroups", page, response));
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += "客服列表获取异常";
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "userGroups", page, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 分页查询所有客服组数据
     */
    private List<ImUserGroupsResponse> loadAllUserGroups() {
        int pageSize = 100;
        int page = 1;
        List<ImUserGroupsResponse> allGroups = new ArrayList<>();
        ImUserGroupsQueryResponse response;
        do {
            response = userGroups(pageSize, page);
            if (response != null && response.getUserGroups() != null) {
                allGroups.addAll(response.getUserGroups());
            }
            page++;
        } while (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getUserGroups()));
        return allGroups;
    }

    /**
     * 获取所有客服组数据，使用缓存，10分钟内过期
     *
     * @return List<ImUserGroupsResponse>
     */
    public List<ImUserGroupsResponse> getAllUserGroups() {
        try {
            return userGroupsCache.get(ALL_USER_GROUPS_KEY);
        } catch (Exception e) {
            log.error("Failed to load user groups from cache", e);
            return Collections.emptyList();
        }
    }


    public CalllogsResponse agents(Integer page,Integer perPage) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        CalllogsResponse response = null;
        String msg = "UDeskClient.getAgents:";
        try {
            response = udeskClient.agents(email, signVersion, nonce, sign, ts, page,perPage);
            log.info(LogUtil.clientLog("UDeskClient", "agents", page, response));
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += response == null ? "response is null" : response.getCodeMessage();
                if (response == null || response.getAgent() == null || StringUtils.isEmpty(response.getAgent().getCellphone())) {
                    msg += "agent列表获取异常";
                }
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "agents",page, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    public GetCustomerListResponse getCustomerList(Integer page, Integer perPage) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = IdUtil.getSnowflakeNextId();
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        GetCustomerListResponse response = null;
        String msg = "UDeskClient.getCustomerList:";
        try {
            response = udeskClient.getCustomerList(null, null, page, perPage, email, ts, sign, String.valueOf(nonce), signVersion);
            log.info(LogUtil.clientLog("UDeskClient", "getCustomerList", page, response));
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += "客户列表获取失败";
                return null;
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "agents",page, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    public List<GetCustomerListResponse.Customer> getAllCustomers() {
        int page = 1;
        int perPage = 100; // 每页获取100条数据，根据实际情况调整
        List<GetCustomerListResponse.Customer> allCustomers = new ArrayList<>();

        while (true) {
            GetCustomerListResponse response = getCustomerList(page, perPage);
            if (response == null || response.getCustomers() == null || response.getCustomers().isEmpty()) {
                break;
            }
            allCustomers.addAll(response.getCustomers());
            // 假设 meta.total_pages 表示总页数，若当前页已达到总页数，则退出循环
            if (page >= response.getMeta().getTotalPages()) {
                break;
            }
            page++;
        }
        return allCustomers;
    }


    /**
     * 调用 UDesk “客户批量导出”接口（/customers/export）
     *
     * @param filterId  客户过滤器 ID，首次调用可选
     * @param query     关键字搜索，可选（与 filterId 互斥）
     * @param scrollId  上一批返回的 scroll_id；首次调用传 null
     * @return          导出结果封装对象，含 customers / scroll_id / total
     */
    public ExportCustomerResponse exportCustomers(Long filterId,
                                                  String query,
                                                  String scrollId) {

        // 1. 生成鉴权字段
        long ts    = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        long nonce = IdUtil.getSnowflakeNextId();
        String signSrc = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign    = DigestUtils.sha1Hex(signSrc);

        ExportCustomerResponse response = null;
        String msg = "UDeskClient.exportCustomers:";

        try {
            /* 2. 调用 Feign / Retrofit / OKHttp 封装的真正请求方法
             *    注意：后续滚动时只传 scrollId，其余两个筛选参数置 null
             */
            response = udeskClient.exportCustomers(
                    filterId,
                    query,
                    scrollId,
                    email,
                    ts,
                    sign,
                    String.valueOf(nonce),
                    signVersion);

            // 3. 记录调用日志
//            log.info(LogUtil.clientLog("UDeskClient",
//                    "exportCustomers",
//                    scrollId == null ? filterId : scrollId,
//                    response));

            // 4. 校验返回码
            if (Objects.isNull(response) || response.getCode() != 1000) {
                msg += "客户批量导出失败";
                return null;
            }
            return response;

        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient",
                    "exportCustomers",
                    scrollId == null ? filterId : scrollId,
                    response,
                    msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    /**
     * 获取客户过滤器列表
     *
     * GET /customers/filters
     */
    public GetCustomerFilterListResponse getCustomerFilters() {

        // 1. 鉴权字段
        long ts    = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        long nonce = IdUtil.getSnowflakeNextId();
        String signSrc = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign    = DigestUtils.sha1Hex(signSrc);

        GetCustomerFilterListResponse response = null;
        String msg = "UDeskClient.getCustomerFilters:";

        try {
            // 2. 真正的 HTTP 调用（无需额外业务参数）
            response = udeskClient.getCustomerFilters(
                    email,
                    ts,
                    sign,
                    String.valueOf(nonce),
                    signVersion);

            // 3. 记录调用日志
            log.info(LogUtil.clientLog("UDeskClient", "getCustomerFilters", null, response));

            // 4. 返回码校验
            if (response == null || response.getCode() != 1000) {
                msg += "过滤器列表获取失败";
                return null;
            }
            return response;

        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient",
                    "getCustomerFilters",
                    null,
                    response,
                    msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 合并两个客户
     *
     * POST /customers/merge
     *
     * @param fromType     被删除客户的条件类型（email / cellphone / customer_token / …）
     * @param fromContent  被删除客户的条件内容
     * @param toType       保留客户的条件类型
     * @param toContent    保留客户的条件内容
     * @return             合并结果
     */
    public MergeCustomerResponse mergeCustomers(String fromType,
                                                String fromContent,
                                                String toType,
                                                String toContent) {

        // 1. 鉴权字段
        long ts    = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        long nonce = IdUtil.getSnowflakeNextId();
        String signSrc = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign    = DigestUtils.sha1Hex(signSrc);

        // 2. 组装请求体
        Map<String, String> body = new HashMap<>(4);
        body.put("from_type",    fromType);
        body.put("from_content", fromContent);
        body.put("to_type",      toType);
        body.put("to_content",   toContent);

        MergeCustomerResponse response = null;
        String msg = "UDeskClient.mergeCustomers:";

        try {
            // 3. 真正的 HTTP 调用
            response = udeskClient.mergeCustomers(
                    body,
                    email,
                    ts,
                    sign,
                    String.valueOf(nonce),
                    signVersion);

            // 4. 日志
            log.info(LogUtil.clientLog("UDeskClient",
                    "mergeCustomers",
                    fromContent + "->" + toContent,
                    response));

            // 5. 返回码校验
            if (response == null || response.getCode() != 1000) {
                msg += "合并客户失败";
                return null;
            }
            return response;

        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient",
                    "mergeCustomers",
                    fromContent + "->" + toContent,
                    response,
                    msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 创建客户
     *
     * POST /customers
     *
     * @param customer     必填：客户主体信息（nickName 必填；email / cellphones… 可选）
     * @return             创建结果
     */
    public CreateCustomerResponse createCustomer(
            Customer customer) {

        // 1. 鉴权字段
        long ts    = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        long nonce = IdUtil.getSnowflakeNextId();
        String signSrc = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign    = DigestUtils.sha1Hex(signSrc);

        // 2. 组装请求体
        Map<String, Object> body = new HashMap<>(3);
        body.put("customer", customer);


        CreateCustomerResponse response = null;
        String msg = "UDeskClient.createCustomer:";

        try {
            // 3. 真正的 HTTP 调用
            response = udeskClient.createCustomer(
                    body,
                    email,
                    ts,
                    sign,
                    String.valueOf(nonce),
                    signVersion);

            // 4. 日志
            log.info(LogUtil.clientLog("UDeskClient",
                    "createCustomer",
                    customer.getNickName(),
                    response));

            // 5. 返回码校验
            if (response == null || response.getCode() != 1000) {
                msg += "创建客户失败";
                return null;
            }
            return response;

        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient",
                    "createCustomer",
                    customer.getNickName(),
                    response,
                    msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    /**
     * 获取所有客服坐席数据，使用缓存，10分钟内有效
     * @return List<AgentsResponse>
     */
    public List<AgentsResponse> getAllAgents() {
        try {
            return agentsCache.get(ALL_AGENTS_KEY);
        } catch (Exception e) {
            log.error("Failed to load agents from cache", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取客服id和客服对应关系
     * @return
     */
    public Map<Integer, AgentsResponse> getAgentsMap() {
        List<AgentsResponse> agents = getAllAgents();
        if (CollectionUtils.isEmpty(agents)) {
            return Collections.emptyMap();
        }
        return agents.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(AgentsResponse::getId, Function.identity(), (a, b) -> a));
    }

    private List<AgentsResponse> loadAllAgents() {
        Integer pageSize = 100;
        Integer page = 1;
        List<AgentsResponse> allAgents = new ArrayList<>();
        CalllogsResponse response;
        do {
            response = agents(page, pageSize);
            if (response != null && CollectionUtils.isNotEmpty(response.getAgents())) {
                allAgents.addAll(response.getAgents());
            }
            page++;
        } while (response != null && CollectionUtils.isNotEmpty(response.getAgents()));
        return allAgents;
    }

    /**
     * 满意度查询接口
     * @return List<ImUserGroupsResponse>
     */
    public SessionsVoteResponse sessionsVote(Integer pageSize, Integer page, String startTime, String endTime) {
        Long ts = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        Long nonce = System.currentTimeMillis() + random.nextInt(999999);
        String value = email + "&" + openApi + "&" + ts + "&" + nonce + "&" + signVersion;
        String sign = DigestUtils.sha1Hex(value);

        SessionsVoteResponse response = null;
        String msg = "UDeskClientError.sessionsVote 满意度查询: 查询时间段 " + startTime + " 到 " + endTime;
        try {
            response = udeskClient.sessionsVote(email, signVersion, nonce, sign, ts, page, pageSize, startTime, endTime);
            log.info(LogUtil.clientLog("UDeskClient", "sessionsVote", page, response));
            if (Objects.isNull(response) || response.getStatus() != 0) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UDeskClient", "sessionsVote", page, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}