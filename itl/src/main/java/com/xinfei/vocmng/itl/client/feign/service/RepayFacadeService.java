/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.service;

import com.xinfei.repaytrade.facade.rr.request.RepayLoanCalcRequest;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentsByLoanNoResponse;
import com.xinfei.repaytrade.facade.rr.response.RepayLoanCalcResponse;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.service.dto.RepayCalculateDto;
import com.xinfei.vocmng.itl.model.enums.SettleBaffleScene;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ LendQueryClientService, v 0.1 2024-05-27 20:24 junjie.yan Exp $
 */
@Service
public class RepayFacadeService {

    @Resource
    private RepayFacadeClient repayFacadeClient;

    /**
     * 返回试算对象
     * 1. 是否支持提前还当期，账单维度，传参：loanNo、calcSettleTypeEnum（NO_SETTLE）、terms、needRepayCheck=true
     * 2. 挡板金额，借据维度，传参：loanNo、calcSettleTypeEnum（SETTLE）、settleLimitCalcFlag（on）
     * 3. 提前结清费，借据维度，传参：loanNo、calcSettleTypeEnum（SETTLE）
     * <p>
     * transUnprofitDeduct：抵扣金额
     */
    @NotNull
    public RepayLoanCalcResponse repayCalculate(Integer repayType, List<String> terms, String loanNo, BigDecimal transUnprofitDeduct, String settleLimitCalcFlag) {
        RepayLoanCalcResponse response;

        if (repayType == 1) {
            //还当期
            response = repayFacadeClient.repayCalculate(loanNo, RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE, settleLimitCalcFlag, terms, true, transUnprofitDeduct);
        } else {
            //结清
            response = repayFacadeClient.repayCalculate(loanNo, RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE, settleLimitCalcFlag == null ? "on" : settleLimitCalcFlag, null, null, transUnprofitDeduct);
        }

        if (response == null) {
            return new RepayLoanCalcResponse();
        } else {
            return response;
        }
    }

    /**
     * 返回应还金额及减免金额(非营销减免金额,有减免方案时,该金额与减免方案金额一致)
     *
     * @param repayType
     * @param terms
     * @param loanNo
     * @return
     */
    @NotNull
    public RepayCalculateDto repayCalculateDto(Integer repayType, List<String> terms, String loanNo, String settleLimitCalcFlag, BigDecimal transUnprofitDeduct) {
        RepayLoanCalcResponse response = repayCalculate(repayType, terms, loanNo, transUnprofitDeduct, settleLimitCalcFlag);
        RepayCalculateDto repayCalculateDto = new RepayCalculateDto();

        if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(response.getSettleBaffleScene())) {
            repayCalculateDto.setRealAmt(response.getTotalAmt().subtract(response.getDeductAmt()).subtract(response.getUnprofitDeductAmt()).subtract(response.getRedDeductAmt()));
        } else {
            repayCalculateDto.setRealAmt(response.getTotalAmt().subtract(response.getDeductAmt()).subtract(response.getUnprofitDeductAmt()));
        }

        repayCalculateDto.setDeductAmt(response.getUnprofitDeductAmt());
        return repayCalculateDto;
    }

    public BigDecimal getActRepayAmt(String loanNo) {
        int page = 1, pageSize = 100;
        QueryRepaymentsByLoanNoResponse response;
        List<QueryRepaymentsByLoanNoResponse.RepaymentInfo> repaymentInfos = new ArrayList<>();
        do {
            response = repayFacadeClient.getRepayments(Collections.singletonList(loanNo), null, null, page, pageSize);
            if (response != null && CollectionUtils.isNotEmpty(response.getRepaymentInfos())) {
                repaymentInfos.addAll(response.getRepaymentInfos());
            } else {
                break;
            }
            page++;
        } while (CollectionUtils.isNotEmpty(response.getRepaymentInfos()));
        return repaymentInfos.stream().map(QueryRepaymentsByLoanNoResponse.RepaymentInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}