<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.vocmng</groupId>
        <artifactId>vocmng</artifactId>
        <version>1.0.4.20230904</version>
    </parent>
    <artifactId>itl</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.cloud</groupId>-->
        <!--            <artifactId>spring-cloud-openfeign-core</artifactId>-->
        <!--        </dependency>-->
        <!--xfframework-->
        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-dependency</artifactId>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <!-- 借款引擎 -->
        <dependency>
            <groupId>com.xinfei.lendtrade</groupId>
            <artifactId>lendtrade-facade</artifactId>
            <version>${lendtrade-facade.version}</version>
        </dependency>

        <!-- 还款记录 -->
        <dependency>
            <groupId>com.xinfei.repaytrade</groupId>
            <artifactId>facade</artifactId>
            <version>${repaytrade-facade.version}</version>
        </dependency>

        <!-- 收银台 -->
        <dependency>
            <groupId>com.xinfei.cashiercore</groupId>
            <artifactId>cashiercore-common-service-facade</artifactId>
            <version>1.0.1.20240830</version>
            <exclusions>
                <exclusion>
                    <artifactId>xinfei-common-lang</artifactId>
                    <groupId>com.xinfei.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- dynamic-datasource 多数据源-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${dynamic-ds.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.fundcore</groupId>
            <artifactId>facade</artifactId>
            <version>${fundcore-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--cis查询相关-->
        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>cis-query-facade</artifactId>
            <version>${cis-query-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.contractcore</groupId>
            <artifactId>contractcore-common-service-facade</artifactId>
            <version>${contractcore.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>user-auth-core-facade</artifactId>
            <version>********.4.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>cis-ext-info-facade</artifactId>
            <version>********.2.RELEASE</version>
        </dependency>

        <dependency>
            <artifactId>cis-secure-client</artifactId>
            <groupId>com.xyf.user</groupId>
            <version>********</version>
        </dependency>

        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>bank-core-facade</artifactId>
            <version>********</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.vipcore</groupId>
            <artifactId>vipcore-facade</artifactId>
            <version>${vip-core.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.common</groupId>
            <artifactId>xinfei-common-lang</artifactId>
            <version>1.0.0.********</version>
        </dependency>
        <dependency>
            <groupId>com.xyf.common</groupId>
            <artifactId>xyf-common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.ssomng</groupId>
            <artifactId>ssomng-facade</artifactId>
            <version>${ssomng.version}</version>
        </dependency>

        <!--ams-->
        <dependency>
            <groupId>io.kyoto.pillar</groupId>
            <artifactId>ams-api</artifactId>
            <version>3.0.71-RELEASE</version>
        </dependency>

        <!--lcs-->
        <dependency>
            <groupId>io.kyoto.pillar</groupId>
            <artifactId>lcs-api</artifactId>
            <version>1.0.27-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>io.kyoto</groupId>
                    <artifactId>sole-api</artifactId>
                </exclusion>
                <!-- 项目使用log4j2日志，去除springboot默认日志依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.kyoto</groupId>
            <artifactId>sole-api</artifactId>
            <version>6.0.1-apollo-encrypt</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>feign-core</artifactId>
                    <groupId>io.github.openfeign</groupId>
                </exclusion>


                <exclusion>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xinfei.listcore</groupId>
            <artifactId>listcore-facade</artifactId>
            <version>1.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.cisaggs</groupId>
            <artifactId>cisaggs-facade</artifactId>
            <version>20250522.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.huttalegal</groupId>
            <artifactId>huttalegal-facade</artifactId>
            <version>${huttalegal-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.psenginecore</groupId>
            <artifactId>psenginecore-facade</artifactId>
            <version>${psenginecore.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xinfei.psenginecore</groupId>
                    <artifactId>psenginecore-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xinfei.vqcprod</groupId>
            <artifactId>vqcprod-facade</artifactId>
            <version>${vqcprod.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.supervip</groupId>
            <artifactId>supervip-interfaces</artifactId>
            <version>${supervip-interfaces.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.supervip</groupId>
            <artifactId>supervip-common</artifactId>
            <version>${supervip-common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xyf.common</groupId>
            <artifactId>xf-random-generator-client</artifactId>
            <version>20250610.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.vocmng</groupId>
            <artifactId>util</artifactId>
        </dependency>
    </dependencies>
</project>
